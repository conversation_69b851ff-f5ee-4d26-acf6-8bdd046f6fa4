<?php

namespace App\Http\Controllers;

use App\Http\Middleware\Auth;
use App\Models\Setting;
use Illuminate\Http\Request;
use App\Models\QueryLog;
use Illuminate\Support\Facades\DB;
use function PHPUnit\Framework\isNull;

class OtosorguController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index($plaka = null,$sasi = null)
    {

    }

    public function hasar($uuid = null,$plaka = null,$type = null,$tc = null,$date = null,$degisenValue = null)
    {
        $authUser = auth()->user();
        $typeMapping = [
            'hasar' => 'hasar',
            'kilometre' => 'kilometre',
            'borc' => 'borc',
            'AracDetay' => 'detail',
            'Degisen' => 'degisen',
            'Ruhsat' => 'ruhsat',
        ];
        $data = QueryLog::where("uuid", $uuid)->first();

        if ($data && isset($typeMapping[$type]) && !is_null($data->{$typeMapping[$type]}) && $type != 'borc') {
            $lastExtraQuery = DB::table('query_logs_extra')
                ->where('expertise_uuid', $uuid)
                ->where('type',$typeMapping[$type])
                ->orderBy('id','desc')
                ->first();
            if ($lastExtraQuery){
                $jsonData = $lastExtraQuery->result;
            }else{
                $field = $typeMapping[$type];
                $jsonData = json_decode($data->{$field}, true);
            }
            return response()->json($jsonData['data']);
        }

        if ($date == null)
            $date = date("Y-m-d");
        $url = env("OTOSORGU_URL");
        $postData = array(
            'sandbox' => "false",
            'query' => $type,
            'apikey' => env("OTOSORGU_KEY"),
            'datatype' => "Plaka",
            'date' => $date,
            'data' => $plaka,
            'idnumber' => $tc,
            'bayi' => $authUser->getBranch->kod ?? '',
            'alan1'=>$authUser->getBranch->kod ?? '',
            'alan2'=>$authUser->id,
            'alan3'=>"alan1=bayi kod",
            'alan4'=>"alan2=personel kod",
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);
        $res = json_decode($response);
        $settings = Setting::first();



        if($res->success){

            if ($type == "hasar"){
                if ($data !== null) {
                    if (!is_null($data->hasar)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type],
                            'result' => json_encode($res),
                            'amount' => $res->data->amount,
                            'commission' => $res->data->amount > 0 ? $settings->hasar_sorgu_komisyon : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $amount = $res->data->amount > 0 ? $res->data->amount:$data->hasar_ucret;
                        $data->update([
                            "user_id" => $authUser->id,
                            "hasar"=>json_encode($res),
                            "hasar_ucret"=>$amount,
                            "hasar_komisyon"=>$amount > 0 ? $settings->hasar_sorgu_komisyon : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "hasar"=>json_encode($res),
                        "hasar_ucret" => $res->data->amount,
                        "hasar_komisyon"=>$res->data->amount > 0 ? $settings->hasar_sorgu_komisyon : 0,
                    ]);
                }
                if (isset($res->data->amount) && $res->data->amount != 0 && $res->data->amount != $settings->hasar_sorgu_otosorgu){
                    $settings->hasar_sorgu_otosorgu = $res->data->amount;
                    $settings->save();
                }
            }
            else if($type == "kilometre"){

                if ($data !== null) {
                    if (!is_null($data->kilometre)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type],
                            'result' => json_encode($res),
                            'amount' => $res->data->amount,
                            'commission' => $res->data->amount > 0 ? $settings->kilometre_sorgu_komisyon : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $amount = $res->data->amount > 0 ? $res->data->amount:$data->kilometre_ucret;
                        $data->update([
                            "user_id" => $authUser->id,
                            "kilometre"=>json_encode($res),
                            "kilometre_ucret"=>$amount,
                            "kilometre_komisyon"=>$amount > 0 ? $settings->kilometre_sorgu_komisyon : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "kilometre"=>json_encode($res),
                        "kilometre_ucret" => $res->data->amount,
                        "kilometre_komisyon"=>$res->data->amount > 0 ? $settings->kilometre_sorgu_komisyon : 0,

                    ]);
                }
                if (isset($res->data->amount) && $res->data->amount != 0 && $res->data->amount != $settings->kilometre_sorgu_otosorgu){
                    $settings->kilometre_sorgu_otosorgu = $res->data->amount;
                    $settings->save();
                }
            }
            else if($type == "borc"){
                if ($data !== null) {
                    if (!is_null($data->borc)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type],
                            'result' => json_encode($res),
                            'amount' => $res->data->amount,
                            'commission' => $res->data->amount > 0 ? $settings->borc_sorgu_komisyon : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $amount = $res->data->amount > 0 ? $res->data->amount:$data->borc_ucret;

                        $data->update([
                            "user_id" => $authUser->id,
                            "borc"=>json_encode($res),
                            "borc_ucret"=>$amount,
                            "borc_komisyon"=>$amount > 0 ? $settings->borc_sorgu_komisyon : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "borc"=>json_encode($res),
                        "borc_ucret" => $res->data->amount,
                        "borc_komisyon"=>$res->data->amount > 0 ? $settings->borc_sorgu_komisyon : 0,
                    ]);
                }
                if (isset($res->data->amount) && $res->data->amount != 0 && $res->data->amount != $settings->borc_sorgu_otosorgu){
                    $settings->borc_sorgu_otosorgu = $res->data->amount;
                    $settings->save();
                }
            }
            else if($type == "AracDetay"){
                if ($data !== null) {
                    if (!is_null($data->detail)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type],
                            'result' => json_encode($res),
                            'amount' => $res->data->amount,
                            'commission' => $res->data->amount > 0 ? $settings->detail_sorgu_komisyon : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $amount = $res->data->amount > 0 ? $res->data->amount:$data->detail_ucret;
                        $data->update([
                            "user_id" => $authUser->id,
                            "detail"=>json_encode($res),
                            "detail_ucret"=>$amount,
                            "detail_komisyon"=>$amount > 0 ? $settings->detail_sorgu_komisyon : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "detail"=>json_encode($res),
                        "detail_ucret" =>$res->data->amount,
                        "detail_komisyon"=>$res->data->amount > 0 ? $settings->detail_sorgu_komisyon : 0,
                    ]);
                }
                if (isset($res->data->amount) && $res->data->amount != 0 && $res->data->amount != $settings->detail_sorgu_otosorgu){
                    $settings->detail_sorgu_otosorgu = $res->data->amount;
                    $settings->save();
                }
            }
            else if($type == "Degisen"){
                if ($data !== null) {
                    $amount = $res->data->amount;
                    $commission = $amount > 0 ? $settings->degisen_sorgu_komisyon : 0;
                    $replacementResponse = json_encode($res);

                    DB::table('query_logs_extra')->updateOrInsert([
                        'user_id' => $authUser->id,
                        'expertise_uuid' => $uuid,
                        'type' => $typeMapping[$type],
                        'result' => $replacementResponse,
                        'amount' => $amount,
                        'commission' => $commission,
                    ]);

                    $data->update([
                        "user_id" => $authUser->id,
                        "degisen" => $replacementResponse,
                        "degisen_ucret" => $amount,
                        "degisen_komisyon" => $commission,
                    ]);
                } else {
                    $array = [$res];
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "degisen" => json_encode($array),
                        "degisen_ucret"=>$res->data->amount,
                        "degisen_komisyon"=>$res->data->amount > 0 ? $settings->degisen_sorgu_komisyon : 0,
                    ]);
                }

                if (isset($res->data->amount) && $res->data->amount != 0 && $res->data->amount != $settings->degisen_sorgu_otosorgu){
                    $settings->degisen_sorgu_otosorgu = $res->data->amount;
                    $settings->save();
                }
                return response()->json($array);
            }
            else if($type == "Ruhsat"){
                if ($data !== null) {
                    if (!is_null($data->ruhsat)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type],
                            'result' => json_encode($res),
                            'amount' => $res->data->price,
                            'commission' => $res->data->price > 0 ? $settings->ruhsat_sorgu_komisyon : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $amount = $res->data->price > 0 ? $res->data->price:$data->ruhsat_ucret;

                        $data->update([
                            "user_id" => $authUser->id,
                            "ruhsat"=>json_encode($res),
                            "ruhsat_ucret"=>$amount,
                            "ruhsat_komisyon"=>$amount > 0 ? $settings->ruhsat_sorgu_komisyon : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "ruhsat"=>json_encode($res),
                        "ruhsat_ucret" => $res->data->price,
                        "ruhsat_komisyon"=>$res->data->price > 0 ? $settings->ruhsat_sorgu_komisyon : 0,
                    ]);
                }
                if (isset($res->data->price) && $res->data->price != 0 && $res->data->price != $settings->ruhsat_sorgu_otosorgu){
                    $settings->ruhsat_sorgu_otosorgu = $res->data->price;
                    $settings->save();
                }
            }
            return response()->json($res->data);
        }
        else{
            return response()->json($response);
        }
    }
    public function sasi($uuid = null,$sasi = null,$type = null,$tc = null,$date = null,$degisenValue = null)
    {
        $authUser = auth()->user();
        $data = QueryLog::where("uuid", $uuid)->first();

        $typeMapping = [
            'hasar' => 'hasar',
            'kilometre' => 'kilometre',
            'borc' => 'borc',
            'AracDetay' => 'detail',
            'Degisen' => 'degisen',
            'Ruhsat' => 'ruhsat',
        ];

        if ($data && isset($typeMapping[$type]) && !is_null($data->{$typeMapping[$type]})  && $type != 'borc') {
            $lastExtraQuery = DB::table('query_logs_extra')
                ->where('expertise_uuid', $uuid)
                ->where('type',$typeMapping[$type])
                ->orderBy('id','desc')
                ->first();
            if ($lastExtraQuery){
                $jsonData = $lastExtraQuery->result;
            }else{
                $field = $typeMapping[$type];
                $jsonData = json_decode($data->{$field}, true);
            }
            return response()->json($jsonData['data']);
        }

        if ($date == null)
            $date = date("Y-m-d");
        $url = env("OTOSORGU_URL");
        $postData = array(
            'sandbox' => "false",
            'query' => $type,
            'apikey' => env("OTOSORGU_KEY"),
            'datatype' => "Sasi",
            'date' => $date,
            'data' => $sasi,
            'idnumber' => $tc,

            'bayi' => $authUser->getBranch->kod ?? '',
            'alan1'=>$authUser->getBranch->kod ?? '',
            'alan2'=>$authUser->id,
            'alan3'=>"alan1=bayi kod",
            'alan4'=>"alan2=personel kod",
        );
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);
        $res = json_decode($response);
        $settings = Setting::first();

        if($res && $res->success){
            $data = QueryLog::where("uuid",$uuid)->first();
            if ($type == "hasar"){
                if ($data !== null) {
                    if (!is_null($data->hasar)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type],
                            'result' => json_encode($res),
                            'amount' => $res->data->amount,
                            'commission' => $res->data->amount > 0 ? $settings->hasar_sorgu_komisyon : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $amount = $res->data->amount > 0 ? $res->data->amount:$data->hasar_ucret;
                        $data->update([
                            "user_id" => $authUser->id,
                            "hasar"=>json_encode($res),
                            "hasar_ucret"=>$amount,
                            "hasar_komisyon"=>$amount > 0 ? $settings->hasar_sorgu_komisyon : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "hasar"=>json_encode($res),
                        "hasar_ucret" => $res->data->amount,
                        "hasar_komisyon"=>$res->data->amount > 0 ? $settings->hasar_sorgu_komisyon : 0,
                    ]);
                }
                if (isset($res->data->amount) && $res->data->amount != 0 && $res->data->amount != $settings->hasar_sorgu_otosorgu){
                    $settings->hasar_sorgu_otosorgu = $res->data->amount;
                    $settings->save();
                }
            } else if($type == "kilometre") {
                if ($data !== null) {
                    if (!is_null($data->kilometre)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type],
                            'result' => json_encode($res),
                            'amount' => $res->data->amount,
                            'commission' => $res->data->amount > 0 ? $settings->kilometre_sorgu_komisyon : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $amount = $res->data->amount > 0 ? $res->data->amount:$data->kilometre_ucret;

                        $data->update([
                            "user_id" => $authUser->id,
                            "kilometre"=>json_encode($res),
                            "kilometre_ucret"=>$amount,
                            "kilometre_komisyon"=>$amount > 0 ? $settings->kilometre_sorgu_komisyon : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "kilometre"=>json_encode($res),
                        "kilometre_ucret" => $res->data->amount,
                        "kilometre_komisyon"=>$res->data->amount > 0 ? $settings->kilometre_sorgu_komisyon : 0,

                    ]);
                }

                if (isset($res->data->amount) && $res->data->amount != 0 && $res->data->amount != $settings->kilometre_sorgu_otosorgu) {
                    $settings->kilometre_sorgu_otosorgu = $res->data->amount;
                    $settings->save();
                }
            }
            else if($type == "borc"){
                if ($data !== null) {
                    if (!is_null($data->borc)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type],
                            'result' => json_encode($res),
                            'amount' => $res->data->amount,
                            'commission' => $res->data->amount > 0 ? $settings->borc_sorgu_komisyon : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $amount = $res->data->amount > 0 ? $res->data->amount:$data->borc_ucret;

                        $data->update([
                            "user_id" => $authUser->id,
                            "borc"=>json_encode($res),
                            "borc_ucret"=>$amount,
                            "borc_komisyon"=>$amount > 0 ? $settings->borc_sorgu_komisyon : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "borc"=>json_encode($res),
                        "borc_ucret" => $res->data->amount,
                        "borc_komisyon"=>$res->data->amount > 0 ? $settings->borc_sorgu_komisyon : 0,
                    ]);
                }
                if (isset($res->data->amount) && $res->data->amount != 0 && $res->data->amount != $settings->borc_sorgu_otosorgu){
                    $settings->borc_sorgu_otosorgu = $res->data->amount;
                    $settings->save();
                }
            }
            else if($type == "AracDetay"){
                if ($data !== null) {
                    if (!is_null($data->detail)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type],
                            'result' => json_encode($res),
                            'amount' => $res->data->amount,
                            'commission' => $res->data->amount > 0 ? $settings->detail_sorgu_komisyon : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $amount = $res->data->amount > 0 ? $res->data->amount:$data->detail_ucret;
                        $data->update([
                            "user_id" => $authUser->id,
                            "detail"=>json_encode($res),
                            "detail_ucret"=>$amount,
                            "detail_komisyon"=>$amount > 0 ? $settings->detail_sorgu_komisyon : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "detail"=>json_encode($res),
                        "detail_ucret" =>$res->data->amount,
                        "detail_komisyon"=>$res->data->amount > 0 ? $settings->detail_sorgu_komisyon : 0,
                    ]);
                }
                if (isset($res->data->amount) && $res->data->amount != 0 && $res->data->amount != $settings->detail_sorgu_otosorgu){
                    $settings->detail_sorgu_otosorgu = $res->data->amount;
                    $settings->save();
                }
            } else if($type == "Degisen") {
                $amount = $res->data->amount > 0 ? $res->data->amount : $settings->degisen_sorgu_otosorgu;
                $commission = $amount > 0 ? $settings->degisen_sorgu_komisyon : 0;
                $replacementResponse = json_encode($res);

                if ($data !== null) {
                    $data->update([
                        "user_id" => $authUser->id,
                        "degisen" => $replacementResponse,
                        "degisen_ucret" => $amount,
                        "degisen_komisyon" => $commission,
                    ]);
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "degisen" => $replacementResponse,
                        "degisen_ucret"=> $amount,
                        "degisen_komisyon"=> $commission,
                    ]);
                }

                DB::table('query_logs_extra')->insert([
                    'user_id' => $authUser->id,
                    'expertise_uuid' => $uuid,
                    'type' => $typeMapping[$type],
                    'result' => $replacementResponse,
                    'amount' => $amount,
                    'commission' => $commission,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                if (isset($res->data->amount) && $res->data->amount != 0 && $res->data->amount != $settings->degisen_sorgu_otosorgu) {
                    $settings->degisen_sorgu_otosorgu = $res->data->amount;
                    $settings->save();
                }
            } else if($type == "Ruhsat") {
                $amount = $res->data->amount > 0 ? $res->data->amount : $data->ruhsat_ucret;
                $commission = $amount > 0 ? $settings->ruhsat_sorgu_komisyon : 0;
                $replacementResponse = json_encode($res);

                if ($data !== null) {
                    $data->update([
                        "user_id" => $authUser->id,
                        "degisen" => $replacementResponse,
                        "degisen_ucret" => $amount,
                        "degisen_komisyon" => $commission,
                    ]);
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "ruhsat"=> $replacementResponse,
                        "ruhsat_ucret" => $amount,
                        "ruhsat_komisyon"=> $commission,
                    ]);
                }

                // We insert every query for logging
                DB::table('query_logs_extra')->insert([
                    'user_id' => $authUser->id,
                    'expertise_uuid' => $uuid,
                    'type' => $typeMapping[$type],
                    'result' => $replacementResponse,
                    'amount' => $amount,
                    'commission' => $commission,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // If api price id different, we save new price to db
                if (isset($res->data->price) && $res->data->price != 0 && $res->data->price != $settings->ruhsat_sorgu_otosorgu) {
                    $settings->ruhsat_sorgu_otosorgu = $res->data->price;
                    $settings->save();
                }
            }

            return response()->json($res->data);
        } else {
            return response()->json($response);
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
