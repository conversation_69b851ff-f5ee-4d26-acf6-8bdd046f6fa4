<?php

namespace Database\Seeders;

use App\Models\Expertise;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        /*
        \App\Models\User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('123456'),
            'branch_id' => 52,
        ]);
        */

        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::statement('SET NAMES utf8;');
        DB::statement('SET sql_mode = \'NO_AUTO_VALUE_ON_ZERO\';');

        DB::table('branches')->truncate();
        DB::table('campaigns')->truncate();
        DB::table('car_fuels')->truncate();
        DB::table('car_groups')->truncate();
        DB::table('stocks')->truncate();
        DB::table('user_role_groups')->truncate();
        DB::table('users')->truncate();
        DB::table('user_branches2')->truncate();
        DB::table('cars')->truncate();
        DB::table('expertises')->truncate();
        DB::table('customers')->truncate();
        DB::table('plus_cards')->truncate();
        DB::table('menus')->truncate();
        DB::table('settings')->truncate();
        DB::table('user_roles')->truncate();
        DB::table('stocks')->truncate();
        DB::table('stock_prices')->truncate();
        DB::table('stock_units')->truncate();
        DB::table('stock_types')->truncate();
        DB::table('cities')->truncate();
        DB::table('zone_branches')->truncate();
        DB::table('plus_card_credi_and_puan_add')->truncate();
        DB::table('plus_card_credi_and_puan_removes')->truncate();

        $this->call([
            BranchCategorySeeder::class,
            BranchCategoryUserLimitSeeder::class,
            DepartmentSeeder::class,
            PlusCardCampaignSeeder::class,
            CarGearSeeder::class,
            CarCaseTypeSeeder::class,
            PlusCardDefinitionPriceSeeder::class,
            ZoneSeeder::class,
            CustomerGroupSeeder::class,
            CustomerTypeSeeder::class,
            StockGroupSeeder::class,
            ExpertiseSeeder::class,
        ]);

        try {
            DB::beginTransaction();

            $this->command->info('Databases truncated!');

            $sqlPath = database_path('seeders/sample_data.sql');

            if (File::exists($sqlPath)) {
                $sql = File::get($sqlPath);

                DB::unprepared($sql);
                DB::commit();

                $this->command->info('sample_data.sql successfully seeded!');
            } else {
                throw new \Exception('sample_data.sql not found!');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error('Seeder hatası: ' . $e->getMessage());
        }
    }
}
