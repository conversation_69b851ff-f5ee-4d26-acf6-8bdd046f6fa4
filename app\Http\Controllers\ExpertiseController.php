<?php

namespace App\Http\Controllers;

use App\Exports\ExpertisesExport;
use App\Http\Middleware\Auth;
use App\Jobs\ExpertisesExportJob;
use App\Models\BodyworkCoordinate;
use App\Models\Branch;
use App\Models\BranchTse;
use App\Models\Campaign;
use App\Models\Car;
use App\Models\CarGroup;
use App\Models\ComponentDyno;
use App\Models\Contract;
use App\Models\ContractBranch;
use App\Models\ContractCode;
use App\Models\ContractStock;
use App\Models\ContractTarget;
use App\Models\Customer;
use App\Models\Expertise;
use App\Models\ExpertiseBodywork;
use App\Models\ExpertiseBodyworkNote;
use App\Models\ExpertiseBrake;
use App\Models\ExpertiseBrakeNote;
use App\Models\ExpertiseCo2;
use App\Models\ExpertiseCo2Note;
use App\Models\ExpertiseComponent;
use App\Models\ExpertiseComponentNote;
use App\Models\ExpertiseDiagnostic;
use App\Models\ExpertiseDiagnosticNote;
use App\Models\ExpertiseInternalControl;
use App\Models\ExpertiseInternalControlsNote;
use App\Models\ExpertisePayment;
use App\Models\ExpertisePlusCardSmsVerification;
use App\Models\ExpertiseReportDownload;
use App\Models\ExpertiseSavedFilter;
use App\Models\ExpertiseStock;
use App\Models\ExpertiseSubControlAndEngine;
use App\Models\ExpertiseSubControlAndEngineNote;
use App\Models\ExpertiseTireAndRim;
use App\Models\ExpertiseTireAndRimNote;
use App\Models\Note;
use App\Models\OldExpertiseNote;
use App\Models\PlusCard;
use App\Models\PlusCardCrediAndPuanRemove;
use App\Models\PlusCardsDefinitions;
use App\Models\PlusCardStock;
use App\Models\PlusCardCrediAndPuanAdd;
use App\Models\QueryLog;
use App\Models\QueryLogExtra;
use App\Models\Setting;
use App\Models\Stock;
use App\Models\T400SRVBASLIK;
use App\Models\User;
use App\Models\Zone;
use App\Models\ZoneBranch;
use App\Services\ExpertiseBodyworkService;
use App\Services\ExpertiseBrakeService;
use App\Services\ExpertiseComponentService;
use App\Services\ExpertiseInternalControlService;
use App\Services\ExpertiseService;
use App\Services\ExpertiseSubControlAndEngineService;
use App\Services\ExpertiseTireAndRimService;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Calculation\MathTrig\Exp;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Debugbar;

class ExpertiseController extends Controller
{
    public function __construct(
        private ExpertiseService                    $expertiseService,
        private ExpertiseBodyworkService            $expertiseBodyworkService,
        private ExpertiseBrakeService               $expertiseBrakeService,
        private ExpertiseInternalControlService     $expertiseInternalControlService,
        private ExpertiseTireAndRimService          $expertiseTireAndRimService,
        private ExpertiseSubControlAndEngineService $expertiseSubControlAndEngineService,
        private ExpertiseComponentService           $expertiseComponentService
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        /** @var  User $authUser */
        $authUser = auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key', ['list_expertise'])->first())
            return redirect()->route('index')->with('error', 'Yetkiniz Yok!');

        $filters = [];
        $savedFilters = ExpertiseSavedFilter::where('user_id',$authUser->id)->get();
        $selectedSavedFilter = isset($_GET['saved_filter_id']) && (int)$_GET['saved_filter_id'] > 0 ? ExpertiseSavedFilter::where(['user_id'=>$authUser->id,'id'=>(int)$_GET['saved_filter_id']])->first() : null;

        if (in_array($authUser->user_role_group_id,[33,34]))
            return redirect()->route('ekspertizIslemleri');

        if ($request->get('customer_id', 0)) {
            $customer = Customer::find($request->get('customer_id'));
        }

        if ($request->get('zone_id', 0)) {
            $filters['zone_id'] = $request->get('zone_id');
        }

        $zones = [];
        if ($authUser->isAdmin()) {
            $zones = Zone::where('status', 1)->limit(30)->get();
        } elseif ($authUser->zone_id > 0) {
            $zones = Zone::where('id', $authUser->zone_id)->where('status', 1)->limit(30)->get();
        }

        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        if (!empty($startDate)) {
            $filters['startDate'] = Carbon::make($request->get('start_date'))->format('Y-m-d');
        } else {
            $filters['startDate'] = Carbon::now()->format('Y-m-d');
        }

        if (!empty($endDate)) {
            $filters['endDate'] = Carbon::make($request->get('end_date'))->format('Y-m-d');
        } else {
            $filters['endDate'] = Carbon::now()->format('Y-m-d');
        }

        $google_button_1 = null;
        $google_button_2 = null;

        // If user is a "Admin" (user_role_group_id = 30)
        // then get the google_button_1 and google_button_2 from the branch with branch_id = 26
        if ($authUser->isAdmin()) {
            $userBranch = $authUser->branches->firstWhere('id', 26);

            $google_button_1 = $userBranch?->google_button_1;
            $google_button_2 = $userBranch?->google_button_2;
        } elseif (in_array($authUser->user_role_group_id, [32, 37, 42, 44, 45, 47, 31, 39])) {
            $google_button_1 = $authUser->getBranch()->first()?->google_button_1;
            $google_button_2 = $authUser->getBranch()->first()?->google_button_2;
        }

        return view('pages.expertise.index', [
            'items' => [],
            'filters' => $filters,
            'savedFilters' => $savedFilters,
            'selectedSavedFilter' => $selectedSavedFilter,
            'customer' => $customer ?? null,
            'zones' => $zones,
            'google_button_1' => $google_button_1,
            'google_button_2' => $google_button_2,
        ]);
    }
    public function indexAjax(Request $request){
        $user = auth()->user();


        $startDate = $request->start_date ? $request->start_date : date('Y-m-d');
        $endDate = $request->end_date ? $request->end_date :  date('Y-m-d');
        $startDate = Carbon::make($startDate)->startOfDay();
        $endDate = Carbon::make($endDate)->endOfDay();

        $limit = $request->input('length');
        $limit_app_db = 0;
        $start = $request->input('start');
        $start_app_db = 0;
        $total = 0;

        $branchDetails = getBranchDetails($user,$request->branch_id);
        $branch_id = $branchDetails['branch_id'];
        $branchBelgeKod = $branchDetails['branchBelgeKod'];

        $belgeNo = '';

        if (!empty($request->belge_no['value'])) {
            $belgeNo = $request->belge_no['value'];
        } elseif (!empty($request->belge_no) && !is_array($request->belge_no)) {
            $belgeNo = $request->belge_no;
        }

        $uuid = '';
        if (!empty($request->uuid['value'])) {
            $uuid = $request->uuid['value'];
        } elseif (!empty($request->uuid) && !is_array($request->uuid)) {
            $uuid = $request->uuid;
        }

        $search = '';
        if (!empty($request->search['value'])) {
            $search = $request->search['value'];
        } elseif (!empty($request->search) && !is_array($request->search)) {
            $search = $request->search;
        }


        $type = '';

        if (!empty($request->type['value'])) {
            $type = $request->type['value'];
        } elseif (!empty($request->type) && !is_array($request->type)) {
            $type = $request->type;
        }

        $expertise_campaing = '';
        if(!empty($request->expertise_campaing['value'])){
            $expertise_campaing =$request->expertise_campaing['value'];
        }elseif(!empty($request->expertise_campaing) && !is_array($request->expertise_campaing)){
            $expertise_campaing =$request->expertise_campaing;
        }

        $payment_type = $request->payment_type ?? "all";

        $items = Expertise::query()
            ->where('status', '!=', -1)
            ->where('manuel_save', 1);

//        $items = $items->whereHas('getBranch',function ($getBranch){
//
//        });


        $items= $items
//            ->where('cari_id', '>', 0)
//            ->where('satici_id', '>', 0)
            ->where('alici_id', '>', 0)
            ->where('car_id', '>', 0);

        if (isset($request->contract_no) && !empty($request->contract_no)) {
            $contractPayments = ExpertisePayment::leftJoin('contract_codes', 'contract_codes.code', '=', 'expertise_payments.payment_detail')
                ->where('expertise_payments.payment_code', $request->contract_no)
                ->where('contract_codes.must_payment',0)
                ->whereNull('contract_codes.deleted_at')
                ->whereNull('expertise_payments.deleted_at')
                ->when(isset($request->contract_id) && !empty($request->contract_id),function ($query) use ($request){
                    return $query->where('contract_codes.contract_id',$request->contract_id);
                })
                ->pluck('expertise_payments.expertise_id')
                ->toArray();
            $items = $items->whereIn('id', $contractPayments);
        }

        if ($request->get('customer_id', 0)) {
            $items = $items->where('cari_id', $request->get('customer_id'));
        }

        if ($request->get('zone_id', 0)) {
            $zoneBranches = ZoneBranch::where('zone_id', $request->get('zone_id'))->pluck('branch_id')->toArray();
            $items->whereIn('branch_id', $zoneBranches);
        }

        if ($user->user_role_group_id == 33){
            $items = $items->whereHas('getStocks',function ($query){
               return $query->where('id','!=',10)->orWhere('id','!=',186);
            })->where('ftp_ok',2);
        }elseif($user->user_role_group_id == 34){
            $items = $items->whereHas('getStocks',function ($query){
                return $query->where('id','!=',9)->orWhere('id','!=',187);
            })->where('ftp_ok',2);
        }

//        if(!empty($search) && !empty($startDate)){
//            $items = $items->whereDate('belge_tarihi', '>=', $startDate)
//                ->whereDate('belge_tarihi', '<=', $endDate);
//        }elseif($startDate == now()->format('Y-m-d') && empty($search)){
//            $items = $items->whereDate('belge_tarihi', '>=', now()->format('Y-m-d'))
//                ->whereDate('belge_tarihi', '<=', now()->format('Y-m-d'));
//        }

        if (empty($search)){
            $items = $items->whereDate('belge_tarihi', '>=', $startDate)
                ->whereDate('belge_tarihi', '<=', $endDate);

            $items = $items->whereIn('branch_id',$branch_id);
        }
        $items = $items->when($belgeNo != null, function ($query)  use ($belgeNo){
            return $query->where('belge_no', 'LIKE', "%" . $belgeNo . "%");
        })
        ->when($uuid != null, function ($query)  use ($uuid){
            return $query->where('uuid', 'LIKE', "%" . $uuid . "%");
        })
        ->when(isset($payment_type) && $payment_type != 'all',function ($query) use ($payment_type){
            return $query->whereHas('getPayment',function ($query2) use ($payment_type){
                return $query2->where('type',$payment_type);
            });
        })
        ->when(isset($expertise_campaing) && $expertise_campaing != 'all' && $expertise_campaing != '',function ($query) use ($expertise_campaing){
            return $query->whereHas('getStocks',function ($query2) use ($expertise_campaing){
                return $query2->where('campaign_id',$expertise_campaing);
            });
        })

        ->where(function ($query) use ($user){
            if ($user->type != 'admin')
                return $query->where('yayin_yasagi',0);
        })
        // We commented out the following lines because UMR-DEV-238
        // ->when(isset($request->customer_id) && $request->customer_id != '',function ($query) use ($request){
        //     return $query->where('cari_id',$request->customer_id)->orWhere('satici_id',$request->customer_id)->orWhere('alici_id',$request->customer_id);
        // })
            ->when($search, function ($query) use ($search, $type, $branch_id, $user) {
                if ($type == 'customer' && !empty($search)) {
                    return $query->when(!$user->isAdmin(), function ($query) use ($branch_id) {
                        $query->where('branch_id', $branch_id);
                    })->where(function ($query) use ($search) {
                        $query->whereHas('getCari', function ($query) use ($search) {
                            $query->where('unvan', 'like', "%" . $search . "%");
                        });
                            // We commented out the following lines because UMR-DEV-238
                            // ->orWhereHas('getSatici', function ($query) use ($search) {
                            //     $query->where('unvan', 'like', "%" . $search . "%");
                            // })
                            // ->orWhereHas('getAlici', function ($query) use ($search) {
                            //     $query->where('unvan', 'like', "%" . $search . "%");
                            // });
                    });
                } elseif ($type == 'plaka' && !empty($search)) {
                    $search = convertTurkishToEnglish(str_replace(' ', '', $search));

                    return $query->when(!$user->isAdmin(), function ($query) use ($branch_id) {
                        $query->where('branch_id', $branch_id);
                    })->whereHas('getCar', function ($query) use ($search) {
                        $query->where('plaka', 'LIKE', "%" . $search . "%");
                    });
                } elseif ($type == "sase" && !empty($search)) {
                    $search = convertTurkishToEnglish(str_replace(' ', '', $search));

                    return $query->when(!$user->isAdmin(), function ($query) use ($branch_id) {
                        $query->where('branch_id', $branch_id);
                    })->whereHas('getCar', function ($query) use ($search) {
                        $query->where('sase_no', 'LIKE', "%" . $search . "%");
                    });
                }
            })
            ->orderBy('created_at', 'desc');


        $total = $items->count();

        if($items->count() < ($start+$limit)){
            $limit_app_db = $request->input('length');
            $start_app_db = $request->input('start') - ($request->input('start') / $request->input('length'));
//            $start = $request->input('start');
//            $limit = ($request->input('length') + $start) - $items->count();
        }
        if(empty($limit) || $limit < 0){
            $limit = 0;
        }

        $items = $items->offset($start)->limit($limit)->orderBy('id','desc')->get();

        $items = $items->map(function ($item,$key) use ($user){
            $getPayments = $item->getPayments;
            $getPayment = ExpertisePayment::where('expertise_id',$item->id)->first();
            $getStocks = $item->getStocks;
            $payText = '';
            $contractActive = false;
            if ($getPayments->count() > 1){
                foreach ($getPayments as $payType){
                    $payText .= __('arrays.payment_types')[$payType->type].', ';
                    if ($payType->payment_code && $payType->payment_detail)
                        $contractActive = true;
                }
            }elseif ($getPayments->count() == 1 && !empty($getPayment->type)){
                $payText = __('arrays.payment_types')[$getPayment->type];
                if ($getPayment->payment_code && $getPayment->payment_detail)
                    $contractActive = true;
            }

            if ($contractActive)
                $payText .= ' - Sözleşme';

            $alici = Customer::where('id',$item->alici_id)->select('unvan','ad','soyad')->first();
            $cari = $item->alici_id == $item->cari_id ? $alici : Customer::where('id',$item->cari_id)->select('unvan','ad','soyad')->first();
            $arac = Car::where('id',$item->car_id)->select('plaka','sase_no','km','model_yili','net_agirlik','cekis')->first();
            $stoklar = '';
//            $hizmetTutari = number_format(0,2,',','.');
//            $iskontoTutari = number_format(0,2,',','.');
//            $listeFiyati = !empty($item->getStockhasOne->getPrice->kdv_dahil_fiyat) ? $item->getStockhasOne->getPrice->kdv_dahil_fiyat : number_format(0,2,',','.');
//            if(!empty($getPayment->type) && $getPayment->type == "plus_kart"){
//                if(!empty($getPayment->plus_card_odeme_id)){
//                    $hizmetTutari = !empty($getPayment->getPlusCardOdeme->unit_price) ? round($getPayment->getPlusCardOdeme->unit_price,2) : '';
//                }
//            }else{
//                $hizmetTutari = !empty($getPayments->type) ? $getPayments->sum('amount') : 0;
//                $iskontoTutari = number_format(0,2,',','.');
//            }


            foreach ($getStocks as $getStock){
                $stoklar .= !empty($getStock->getStock->ad) ? $getStock->getStock->ad : '' . ',';
            }


            $button_edit = null;
            if(userHasRole('edit_expertise'))
            {
                $href_edit = route('expertises.edit',$item->uuid);
                if ($user->type == 'admin'){
                    $href_edit = route('expertises.edit',$item->uuid);
                    $href_edit_onizle = route('ekspertizRaporu',$item->uuid);
                    $button_edit = "<a href='$href_edit' class='btn btn-danger btn-sm'>Düzenle</a>";
                    $button_edit .= "<a href='$href_edit_onizle' class='btn btn-success btn-sm' style='margin-left: 3px'>Önizle</a>";
                }else{
                    if($item->isComplete() == "Tamamlandı"){
                        if($item->isCompleteDate()){
                            $href_edit = route('ekspertizRaporu',$item->uuid);
                        }
                        if ($item->yayin_yasagi == 1){
                            $button_edit = "<button class='btn btn-sm btn-danger'>Yayın Yasağı Var!</button>";
                        }else{
                            $button_edit = "<a href='$href_edit' class='btn btn-success btn-sm'>Önizle</a>";
                        }
                    }else{
                        if($item->isCompleteDate()){
                            $href_edit = route('expertises.edit',$item->uuid);
                        }
                        $href_edit_onizle = route('ekspertizRaporu',$item->uuid);
                        $button_edit = "<a href='$href_edit' class='btn btn-danger btn-sm'>Düzenle</a>";
                        if ($item->yayin_yasagi == 1){
                            $button_edit = "<button class='btn btn-sm btn-danger'>Yayın Yasağı Var!</button>";
                        }else{
                            $button_edit .= "<a href='$href_edit_onizle' class='btn btn-success btn-sm' style='margin-left: 3px'>Önizle</a>";
                        }

                    }
                }
            }





            $hasar_sorguTutari = 0;
            $kilometre_sorguTutari = 0;
            $borc_sorguTutari = 0;
            $detail_sorguTutari = 0;
            $degisen_sorguTutari = 0;
            $ruhsat_sorguTutari = 0;
            $hasarSorguRenk = '';
            $kilometreSorguRenk = '';
            $borcSorguRenk = '';
            $detailSorguRenk = '';
            $ruhsatSorguRenk = '';
            $degisenSorguRenk = '';
            if(!empty($item->queryLog)){
                $extraQueries = QueryLogExtra::where('expertise_uuid',$item->uuid)->get();
                $queryLog = $item->queryLog;
                if($queryLog->hasar != null){
                    $hasar = json_decode($queryLog->hasar);
                    if(!empty($queryLog->hasar_ucret)){
                        $hasar_sorguTutari = $queryLog->hasar_ucret + $queryLog->hasar_komisyon;
                    }else{
                        $hasar_sorguTutari = $hasar->data->amount > 0 ? $hasar->data->amount + $queryLog->hasar_komisyon : 0;
                    }
                    $hasar_sorguTutari += $extraQueries->where('type','hasar')->sum(function ($extra){
                        return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                    });
                    if ($hasar_sorguTutari > 0){
                        if (isset($hasar->from) && $hasar->from == "arabasorgula.com"){
                            $hasarSorguRenk = '#e2626b';
                        }else{
                            $hasarSorguRenk = '#fecc16';
                        }
                        if ($user->type != 'admin')
                            $hasarSorguRenk = 'transparent';
                    }

                }
                if($queryLog->kilometre != null){
                    $kilometre = json_decode($queryLog->kilometre);
                    if(!empty($queryLog->kilometre_ucret)){
                        $kilometre_sorguTutari = $queryLog->kilometre_ucret + $queryLog->kilometre_komisyon;
                    }else{
                        $kilometre_sorguTutari = $kilometre->data->amount > 0 ? $kilometre->data->amount + $queryLog->kilometre_komisyon : 0;
                    }
                    $kilometre_sorguTutari += $extraQueries->where('type','kilometre')->sum(function ($extra){
                        return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                    });
                   if ($kilometre_sorguTutari > 0){
                       if (isset($kilometre->from) && $kilometre->from == "arabasorgula.com"){
                           $kilometreSorguRenk = '#e2626b';
                       }else{
                           $kilometreSorguRenk = '#fecc16';
                       }
                       if ($user->type != 'admin')
                           $kilometreSorguRenk = 'transparent';
                   }
                }
                if($queryLog->borc != null){
                    $borc = json_decode($queryLog->borc);
                    if(!empty($queryLog->borc_ucret)){
                        $borc_sorguTutari = $queryLog->borc_ucret + $queryLog->borc_komisyon;
                    }else{
                        $borc_sorguTutari = $borc->data->amount > 0 ? $borc->data->amount + $queryLog->borc_komisyon : 0;
                    }
                    $borc_sorguTutari += $extraQueries->where('type','borc')->sum(function ($extra){
                        return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                    });
                    if ($borc_sorguTutari > 0){
                        if (isset($borc->from) && $borc->from == "arabasorgula.com"){
                            $borcSorguRenk = '#e2626b';
                        }else{
                            $borcSorguRenk = '#fecc16';
                        }
                        if ($user->type != 'admin')
                            $borcSorguRenk = 'transparent';
                    }
                }
                if($queryLog->detail != null){
                    $detail = json_decode($queryLog->detail);
                    if(!empty($queryLog->detail_ucret)){
                        $detail_sorguTutari = $queryLog->detail_ucret + $queryLog->detail_komisyon;
                    }else{
                        $detail_sorguTutari = $detail->data->amount > 0 ? $detail->data->amount + $queryLog->detail_komisyon : 0;
                    }
                    $detail_sorguTutari += $extraQueries->where('type','detail')->sum(function ($extra){
                        return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                    });
                    if ($detail_sorguTutari > 0){
                        if (isset($detail->from) && $detail->from == "arabasorgula.com"){
                            $detailSorguRenk = '#e2626b';
                        }else{
                            $detailSorguRenk = '#fecc16';
                        }
                        if ($user->type != 'admin')
                            $detailSorguRenk = 'transparent';
                    }
                }
                if($queryLog->degisen != null){
                    $degisen = json_decode($queryLog->degisen);
                    if(!empty($queryLog->degisen_ucret)){
                        $degisen_sorguTutari = $queryLog->degisen_ucret + $queryLog->degisen_komisyon;
                    }else{
                        if (is_array($degisen))
                            $degisen_sorguTutari = $degisen[0]->data->amount > 0 ? (($degisen[0]->data->amount * count($degisen)) + ($queryLog->degisen_komisyon * count($degisen))) : 0;
                        else
                            $degisen_sorguTutari = $degisen->data->amount > 0 ? $degisen->data->amount + $queryLog->degisen_komisyon : 0;
                    }
                    $degisen_sorguTutari += $extraQueries->where('type','degisen')->sum(function ($extra){
                        return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                    });
                    if ($degisen_sorguTutari > 0){
                        if (is_array($degisen)){
                            if (isset($degisen[0]->from) && $degisen[0]->from == "arabasorgula.com"){
                                $degisenSorguRenk = '#e2626b';
                            }else{
                                $degisenSorguRenk = '#fecc16';
                            }
                        }else{
                            if (isset($degisen->from) && $degisen->from == "arabasorgula.com"){
                                $degisenSorguRenk = '#e2626b';
                            }else{
                                $degisenSorguRenk = '#fecc16';
                            }
                        }
                        if ($user->type != 'admin')
                            $degisenSorguRenk = 'transparent';
                    }
                }
                if($queryLog->ruhsat != null){
                    $ruhsat = json_decode($queryLog->ruhsat);
                    if(!empty($queryLog->ruhsat_ucret)){
                        $ruhsat_sorguTutari = $queryLog->ruhsat_ucret + $queryLog->ruhsat_komisyon;
                    }else{
                        $ruhsat_sorguTutari = $ruhsat->data->amount > 0 ? $ruhsat->data->amount + $queryLog->ruhsat_komisyon : 0;
                    }
                    $ruhsat_sorguTutari += $extraQueries->where('type','ruhsat')->sum(function ($extra){
                        return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                    });
                    if ($ruhsat_sorguTutari > 0){
                        if (isset($ruhsat->from) && $ruhsat->from == "arabasorgula.com"){
                            $ruhsatSorguRenk = '#e2626b';
                        }else{
                            $ruhsatSorguRenk = '#fecc16';
                        }
                        if ($user->type != 'admin')
                            $ruhsatSorguRenk = 'transparent';
                    }
                }
            }
            if ($getPayment?->type == 'plus_kart' && $getPayment?->plus_card_odeme_id > 0){
                $hizmetTutari = (float)$getPayment?->getPlusCardOdeme?->unit_price;
            }else{
                $payments = ExpertisePayment::where('expertise_id',$item->id)->sum('amount');
                $hizmetTutari = $payments;
            }

            $durum = '<span class="status_span" style="background-color: #ff101082;">Bekleniyor</span>';
            if ($item->status == 3)
                $durum = '<span class="status_span" style="background-color: rgba(147,234,164,0.45);">Ekspertiz Tamamlandı</span>';
            elseif ($item->isComplete())
                $durum = '<span class="status_span" style="background-color: #00ff3591;">Kapandı</span>';

            $getStockhasOne = $item->getStockhasOne;
            $getStockhasOneGetStock = $getStockhasOne?->getStock;
            $registerBranch = $item->getRegisterBranch;
            $branch = $item->branch_id == $item->kayit_branch_id ? $registerBranch : $item->getBranch;

            return [
                'button_edit' => $button_edit,
                'durum' => $durum,
                'aliciUnvan' => $alici->fullName ?? '',
                'cariUnvan'  => $cari?->fullName ?? '',
                'plaka' =>!empty($arac->plaka) ? $arac->plaka:'',
                'sase_no' =>$item->sase_no ? $item->sase_no : ( !empty($arac->sase_no) ? $arac->sase_no:''),
                'km' => !empty($item->km) ? $item->km : (!empty($arac->km) ? $arac->km : ''),
                'model_yili' =>!empty($arac->model_yili) ? $arac->model_yili:'',
                'net_agirlik' =>  !empty($arac->net_agirlik) ? $arac->net_agirlik:'',
                'cekis' =>!empty($arac->cekis) ? $arac->cekis:'',
                'getStocks' => $stoklar,
                'campaign' =>$getStockhasOne->getCampaign->name ?? 'Yok',
                'payment_type' =>$payText,
                'hizmetTutari' => number_format($hizmetTutari,2,',','.')."₺" ?? '0',
                'listeFiyati' => number_format((float)$getStockhasOne?->liste_fiyati ?? 0,2,',','.')."₺" ?? '0',
                'iskonto_amount' => $getStockhasOne?->iskonto_amount."₺",
                'hasar_sorgulama_tutari' => "<span style='background-color: {$hasarSorguRenk}'>".number_format((float)($hasar_sorguTutari),2,',','.') . '₺'."</span>",
                'kilometre_sorgulama_tutari' => "<span style='background-color: {$kilometreSorguRenk}'>".number_format((float)($kilometre_sorguTutari),2,',','.') . '₺'."</span>",
                'borc_sorgulama_tutari' => "<span style='background-color: {$borcSorguRenk}'>".number_format((float)($borc_sorguTutari),2,',','.') . '₺'."</span>",
                'details_sorgulama_tutari' => "<span style='background-color: {$detailSorguRenk}'>".number_format((float)($detail_sorguTutari),2,',','.') . '₺'."</span>",
                'degisen_sorgulama_tutari' => "<span style='background-color: {$degisenSorguRenk}'>".number_format((float)($degisen_sorguTutari),2,',','.') . '₺'."</span>",
                'ruhsat_sorgulama_tutari' => "<span style='background-color: {$ruhsatSorguRenk}'>".number_format((float)($ruhsat_sorguTutari),2,',','.') . '₺'."</span>",
                'belge_tarihi' => date('d.m.Y H:i:s',strtotime($item->belge_tarihi)),
                'cikis_tarihi' => !empty($item->cikis_tarihi) ? date('d.m.Y H:i:s',strtotime($item->cikis_tarihi)) : '',
                'belge_no' => $item->belge_no,
                'belge_ozel_kodu' => $item->belge_ozel_kodu == 1 ? 'Onaylı' : 'Onaysız',
                'nereden_ulastiniz' => $item->nereden_ulastiniz,
                'yayin_yasagi' => $item->yayin_yasagi == 1 ? 'Evet' : 'Hayır',
                'sigorta_teklif_ver' => $item->sigorta_teklif_ver == null || $item->sigorta_teklif_ver == 0 ? 'Hayır' : 'Evet',
                'yol_yardim' =>!empty($getStockhasOneGetStock) && $getStockhasOneGetStock->yol_yardimi == 1 ? 'Evet' : 'Hayır',
                'hasar_sorgulama' => !empty($getStockhasOneGetStock) && $getStockhasOneGetStock->sorgu_hizmeti == 1 ? 'Evet' : 'Hayır',
                'kayitBranch' => $registerBranch?->kisa_ad,
                'sozlesme_var_mi' => $item->payment_type == 'sozlesme' ? 'Evet' : 'Hayır',
                'sozlesme_sahibi' => $item->payment_type == 'sozlesme' ? $getPayment?->getContractCode?->getContract?->getCustomer?->fullName : '',
                'branch' =>$branch?->kisa_ad,
                'audio_url' =>$item->audio_url != null ? 'Alındı' : 'Alınmadı',
                'delete' => userHasRole('delete_expertise') ? " <button class=\"btn btn-danger btn-sm\" type=\"button\" onclick=\"checkBeforeDelete('deleteForm".$key."')\">Sil</button><form method=\"post\" action=\"".route('expertises.destroy',$item)."\" id=\"deleteForm".$key."\"><input type='hidden' name='_token' value='".csrf_token()."'> <input type='hidden' name='_method' value='delete'></form>" : 'Bu İşlemi Silemezsiniz!'
            ];
        })->toArray();


        if (!env('APP_LOCAL') && (!isset($request->payment_type) || $request->payment_type == 'all' || $request->payment_type == 'plus_kart') && ($startDate < '2024-04-01' ||  !is_null($search))){
            $items2 = DB::connection('mysql2')->table('T400_SRVBASLIK')
                ->when(isset($request->payment_type) && $request->payment_type == 'plus_kart',function ($query){
                    return $query->whereNotNull('T400_KUPON_UQ');
                })
                ->when($belgeNo != null, function ($query) use ($belgeNo){
                    return $query->where('T400_belgeno', 'LIKE', "%" . $belgeNo . "%");
                })
                ->when($uuid != null, function ($query) use ($uuid){
                    return $query->where('T400_UQ', 'LIKE', "%" . $uuid . "%");
                })
                ->where(function ($query) use ($user){
                    if ($user->type != 'admin')
                        return $query->where('T400_sitedeyayinyasak',0)->orWhere('T400_sitedeyayinyasak',null);
                })
                ->when(isset($request->customer_id) && $request->customer_id != '',function ($query) use ($request){
                    $customer = Customer::where('id',$request->customer_id)->first();
                    if ($customer)
                        return $query->where('T400_musteri_UQ',$customer->kod);
                })
                ->when($search, function ($query) use ($search,$type) {
                    if ($type == 'customer' && !empty($search)) {
                        $cariler = DB::connection('mysql2')->table('T201_CARILER')
                            ->where('unvan', 'LIKE', "%" . $search . "%")
                            ->orWhere('adi', 'LIKE', "%" . $search. "%")
                            ->orWhere('soyadi', 'LIKE', "%" . $search . "%")
                            ->select('hesap_UQ')->pluck('hesap_UQ')->toArray();

                        return $query->whereIn('T400_musteri_UQ', $cariler);
                    } else if ($type == 'plaka' && !empty($search)) {
                        $araclar = DB::connection('mysql2')->table('T202_ARACLAR')
                            ->where('T202_plakano', 'LIKE', "%" . $search . "%")
                            ->select('T202_UQ')->pluck('T202_UQ')->toArray();

                        return $query->whereIn('T400_arac_UQ', $araclar);
                    } elseif($type == "sase" && !empty($search)){
                        $araclar = DB::connection('mysql2')->table('T202_ARACLAR')
                            ->where('T202_saseno', 'LIKE', "%" . $search . "%")
                            ->select('T202_UQ')->pluck('T202_UQ')->toArray();

                        return $query->whereIn('T400_arac_UQ', $araclar);
                    }
                });
            if($branchBelgeKod != 0){
                if(is_array($branchBelgeKod)){
                    $items2 = $items2->whereIn('T400_subekodu',$branchBelgeKod);
                }else{
                    $items2 = $items2->where('T400_subekodu',$branchBelgeKod);
                }

            }

            if (empty($search) && (!isset($request->customer_id) || $request->customer_id == '')){
                $items2 = $items2->whereDate('T400_belgetarihi', '>=', $startDate)
                    ->whereDate('T400_belgetarihi', '<=', $endDate);
            }

//            if(!empty($request->start_date)){
//                $items2 = $items2->whereDate('T400_belgetarihi', '>=', $startDate)
//                    ->whereDate('T400_belgetarihi', '<=', $endDate);
//            }elseif($startDate == now()->format('Y-m-d') && empty($search)){
//                $items2 = $items2->whereDate('T400_belgetarihi', '>=', now()->format('Y-m-d'))
//                    ->whereDate('T400_belgetarihi', '<=', now()->format('Y-m-d'));
//            }
            $items2 = $items2->select('T400_UQ','T400_yol_yardim','T400_hasar_sorgulama','T400_agirlik','T400_musteri_UQ','T400_arac_UQ','T400_srvsubekodu','T400_satici_UQ','T400_subekodu','T400_hizmet_UQ','T400_belgeozelkodu','T400_belgetarihi','T400_cikistarihi','T400_belgeno','T400_reklamulasimyeri','T400_arackm','T400_sitedeyayinyasak','T400_sigorta_teklifi','T400_tllistefiyati','T400_tlsatistutari');

            $total = $total + $items2->count();


            $items2 = $items2->offset($start_app_db)
                ->limit($limit_app_db)->orderBy('T400_belgetarihi','desc')->get();

            $items2 = $items2->map(function ($item) use ($user){
                $alici = DB::connection('mysql2')->table('T201_CARILER')->where('hesap_UQ',$item->T400_musteri_UQ)->select('unvan','adi','soyadi')->first();
                $cari = DB::connection('mysql2')->table('T201_CARILER')->where('hesap_UQ',$item->T400_satici_UQ)->select('unvan','adi','soyadi')->first();
                $arac = DB::connection('mysql2')->table('T202_ARACLAR')->where('T202_UQ',$item->T400_arac_UQ)->select('T202_saseno','T202_plakano','T202_modelyili','T202_motor_cekis')->first();

                $registerBranch = DB::connection('mysql2')->table('T103_SUBELER')->where('T103_kod',$item->T400_srvsubekodu)->select('T103_kisaadi')->first();
                $branch = DB::connection('mysql2')->table('T103_SUBELER')->where('T103_kod',$item->T400_subekodu)->select('T103_kisaadi')->first();
                $stok = DB::connection('mysql2')->table('T203_STOKLAR')->where('T203_UQ',$item->T400_hizmet_UQ)->select('T203_stokadi')->first();

                if ($user->user_role_group_id == 33 && ($stok->T203_stokadi == 'KAPORTA BOYA EKSPERTİZ' || $stok->T203_stokadi == 'PLUS KAPORTA BOYA EKSPERTİZ')){
                    return null;
                }elseif($user->user_role_group_id == 34 && ($stok->T203_stokadi == 'MOTOR MEKANİK EKSPERTİZ' || $stok->T203_stokadi == 'PLUS MOTOR MEKANİK EKSPERTİZ')){
                    return null;
                }



                $neredenUlasti = DB::connection('mysql2')->table('T122_GRUPDEGER')->where('T122_kod',$item->T400_reklamulasimyeri)->where('T122_grupno',4)->select('T122_aciklama')->first();
                if (!empty($arac->T202_motor_cekis) && $arac->T202_motor_cekis == 1)
                    $cekis = '4x2';
                elseif (!empty($arac->T202_motor_cekis) && $arac->T202_motor_cekis == 2)
                    $cekis = '4x4';
                else
                    $cekis = 'Yok';

                $payment_type = 'Diğer';
                if(!empty($item->T400_KUPON_UQ)){
                    $payment_type = "Plus Card";
                }
                $href_edit = !empty($item->T400_UQ) ? route('ekspertizRaporu',$item->T400_UQ) : '';
                if ($item->T400_sitedeyayinyasak == 1 && $user->type != 'admin')
                    $button_edit = "<a href='#' class='btn btn-danger btn-sm'>Yayın Yasağı Var!</a>";
                else
                    $button_edit = "<a href='$href_edit' class='btn btn-success btn-sm'>Önizle</a>";

                return [
                    'button_edit' => $button_edit,
                    'durum' => '<span class="status_span" style="background-color: #00ff3591;">Tamamlandı</span>',
                    'aliciUnvan' => $alici != null ? ($alici->unvan != '' ? $alici->unvan : ($alici->adi != '' && $alici->soyadi != '' ? $alici->adi . ' ' . $alici->soyadi : '')) : '',
                    'cariUnvan' => isset($cari) && $cari->unvan != '' ? $cari->unvan : ($cari && $cari->adi != '' && $cari->soyadi != '' ? $cari->adi . ' ' . $cari->soyadi : '' ),
                    'plaka' =>$arac->T202_plakano ?? '',
                    'sase_no' =>$arac->T202_saseno ?? '',
                    'km' => $item->T400_arackm ?? '',
                    'model_yili' =>$arac->T202_modelyili ?? '',
                    'net_agirlik' =>  $item->T400_agirlik ?? '',
                    'cekis' =>$cekis,
                    'getStocks' => $stok->T203_stokadi ?? '',
                    'campaign' =>'',
                    'payment_type' =>$payment_type,
                    'listeFiyati' => number_format($item->T400_tllistefiyati,2,',','.')."₺" ?? '0',
                    'hizmetTutari' => number_format($item->T400_tlsatistutari,2,',','.')."₺",
                    'iskonto_amount' =>number_format(($item->T400_tllistefiyati-$item->T400_tlsatistutari),2,',','.')."₺",
                    'belge_tarihi' => date('d.m.Y H:i:s',strtotime($item->T400_belgetarihi)),
                    'cikis_tarihi' => date('d.m.Y H:i:s',strtotime($item->T400_cikistarihi)),
                    'belge_no' => $item->T400_belgeno ?? '',
                    'belge_ozel_kodu' => $item->T400_belgeozelkodu == 1 ? 'Onaylı' : 'Onaysız',
                    'nereden_ulastiniz' => $neredenUlasti->T122_aciklama ?? '',
                    'yayin_yasagi' => $item->T400_sitedeyayinyasak == 1 ? 'Evet' : 'Hayır',
                    'sigorta_teklif_ver' => $item->T400_sigorta_teklifi == null || $item->T400_sigorta_teklifi == 0 ? 'Hayır' : 'Evet',
                    'yol_yardim' =>$item->T400_yol_yardim != null ? 'Evet' : 'Hayır',
                    'hasar_sorgulama' => $item->T400_hasar_sorgulama != null ? 'Evet' : 'Hayır',
                    'kayitBranch' => $registerBranch->T103_kisaadi ?? '',
                    'branch' =>$branch->T103_kisaadi ?? '',
                    'audio_url' =>'-',
                    'sozlesme_var_mi' => 'Hayır',
                    'sozlesme_sahibi' => '',
                    'delete' => 'Eski Kayıtlar Silinemez',
                    'hasar_sorgulama_tutari' => '-₺',
                    'kilometre_sorgulama_tutari' =>  '-₺',
                    'borc_sorgulama_tutari' =>  '-₺',
                    'details_sorgulama_tutari' => '-₺',
                    'degisen_sorgulama_tutari' => '-₺',
                    'ruhsat_sorgulama_tutari' => '-₺',
                ];


            })->filter()->toArray();

            $alls = array_merge($items,$items2);
        }
        else
            $alls = $items;


        $json_data = array(
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($total),
            "recordsFiltered" => intval($total),
            "data"            => $alls,
            'filters'          => ['startDate' => $startDate, 'endDate' => $endDate]
        );
        echo  json_encode($json_data);
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        /** @var \App\Models\User $authUser */
        $authUser = auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['add_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');
        $stocks = Stock::where('status',1)->where('show_on_expertise',1)->orderBy('rank','asc')->get();
        $campaigns = Campaign::where('status',1)->get();
        $branches = $authUser->activeBranches();

        $notCompleteForm = Expertise::where(['user_id'=>$authUser->id,'manuel_save'=>0,'status'=>2])->first();

        if ($notCompleteForm && Carbon::now()->diffInMinutes($notCompleteForm->created_at) > 10) {
            $notCompleteForm->delete();
            ExpertisePayment::where('expertise_id',$notCompleteForm->id)->delete();
            ExpertiseStock::where('expertise_id',$notCompleteForm->id)->delete();
            $message = 'Süre sınırı dolduğu için yeni kayıt açıldı!';
            $notCompleteForm = null;
        }else{
            $message = '';
        }
        if (isset($_GET['customer_id']))
            $getCustomer = \App\Models\Customer::where('id',$_GET['customer_id'])->first();

        if(isset($_GET['customer_id']) && isset($_GET['return_customer_type']) && $_GET['return_customer_type'] == 'ruhsat')
            $ruhsatCustomer = $getCustomer;
        elseif(isset($notCompleteForm) && $notCompleteForm->cari_id > 0)
            $ruhsatCustomer = \App\Models\Customer::where('id',$notCompleteForm->cari_id)->first();
        else
            $ruhsatCustomer = null;

        if(isset($_GET['customer_id']) && isset($_GET['return_customer_type']) && $_GET['return_customer_type'] == 'satici')
            $saticiCustomer = $getCustomer;
        elseif(isset($notCompleteForm) && $notCompleteForm->satici_id > 0)
            $saticiCustomer = \App\Models\Customer::where('id',$notCompleteForm->satici_id)->first();
        else
            $saticiCustomer = null;

        if(isset($_GET['customer_id']) && isset($_GET['return_customer_type']) && $_GET['return_customer_type'] == 'alici')
            $aliciCustomer = $getCustomer;
        elseif(isset($notCompleteForm) && $notCompleteForm->alici_id > 0)
            $aliciCustomer = \App\Models\Customer::where('id',$notCompleteForm->alici_id)->first();
        else
            $aliciCustomer = null;

        if(isset($_GET['car_id']) && !empty($_GET['car_id']) && $_GET['car_id'] != ''){
            if(!empty($notCompleteForm)){
                $notCompleteForm->car_id = $_GET['car_id'];
            }

            $car = \App\Models\Car::where('id',$_GET['car_id'])->first();
        }

        elseif(isset($notCompleteForm) && $notCompleteForm->car_id > 0)
            $car = \App\Models\Car::where('id',$notCompleteForm->car_id)->first();
        else
            $car = null;

        $plusCardSmsCodeCep = 'UMR-'.rand(10,99).'-'.rand(10,99);
        $plusCardSmsCodeTelefon = 'UMR-'.rand(10,99).'-'.rand(10,99);

        // Müşteriye ait promosyon kodunu al
        $customerPromoCode = null;
        if ($aliciCustomer) {
            $customerPromoCode = \App\Models\PromotionCode::where('customer_id', $aliciCustomer->id)
                ->where('is_used', 1)
                ->orderByDesc('created_at')
                ->first();
        }

        return view('pages.expertise.create',compact([
            'stocks',
            'branches',
            'campaigns',
            'notCompleteForm',
            'ruhsatCustomer',
            'saticiCustomer',
            'aliciCustomer',
            'car',
            'message',
            'plusCardSmsCodeCep',
            'plusCardSmsCodeTelefon',
            'customerPromoCode'
        ]));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $authUser = auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['add_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $authUserBranchIds = $authUser->getBranchIds();

        if ((int)$request->cari_id > 0){
            $checkCari = Expertise::where('cari_id',(int)$request->cari_id)->where('manuel_save',0)->count();
            if ($checkCari > 5)
                return back()->with('error','Cariye ait aktif bakiyeleri kapatınız!');
        }elseif ((int)$request->satici_id > 0){
            $checkCari = Expertise::where('satici_id',(int)$request->satici_id)->where('manuel_save',0)->count();
            if ($checkCari > 5)
                return back()->with('error','Cariye ait aktif bakiyeleri kapatınız!');
        }elseif ((int)$request->alici_id > 0){
            $checkCari = Expertise::where('alici_id',(int)$request->alici_id)->where('manuel_save',0)->count();
            if ($checkCari > 5)
                return back()->with('error','Cariye ait aktif bakiyeleri kapatınız!');
        }

        if (!in_array($request->branch_id,$authUserBranchIds) || !in_array($request->kayit_branch_id,$authUserBranchIds))
            return back()->with('error','Seçilen Bayilerde Yetkiniz Bulunmamaktadır.');

        $expertise = Expertise::where('uuid',$request->uuid)->first();

        if (!$expertise){
            $expertise= new Expertise();
            $expertise->uuid = (string) Str::orderedUuid();
            $expertise->belge_no = createBelgeNo();
        }else{
            $checkBelgeNo = Expertise::where('belge_no',$request->belge_no)->where('id','!=',$expertise->id)->first();
            if ($checkBelgeNo)
                $belgeNo = createBelgeNo();
            else
                $belgeNo = $expertise->belge_no;
            $expertise->belge_no = $belgeNo;
        }
        $expertise->cari_id = (int)$request->cari_id;
        $expertise->nereden_ulastiniz = $request->nereden_ulastiniz;
        if (isset($request->satici_ayni))
            $expertise->satici_id = (int)$request->cari_id;
        else
            $expertise->satici_id = (int)$request->satici_id ?? (int)$request->cari_id;
        if (isset($request->alici_ayni))
            $expertise->alici_id = (int)$request->cari_id;
        else
            $expertise->alici_id = (int)$request->alici_id ?? (int)$request->cari_id;
        $expertise->car_id = (int)$request->car_id;
        $expertise->km = $request->km;
        if ($request->sase_no)
            $expertise->sase_no = $request->sase_no;
        elseif((int)$request->car_id > 0){
            $car = Car::where('id',(int)$request->car_id)->first();
            if ($car)
                $expertise->sase_no = $car->sase_no;
        }
        $expertise->km_type = $request->km_type;
        $expertise->belge_tarihi = $request->belge_tarihi;

        $expertise->belge_ozel_kodu = $request->belge_ozel_kodu;
        $expertise->sigorta_teklif_ver = $request->sigorta_teklifi_ver;
        $expertise->yayin_yasagi = $request->yayin_yasagi;
        $expertise->kayit_branch_id = $request->kayit_branch_id != '' ? $request->kayit_branch_id : $authUserBranchIds[0];
        $expertise->branch_id = $request->branch_id != '' ? $request->branch_id : $authUserBranchIds[0];
        $expertise->cikis_tarihi = $request->cikis_tarihi;
        if ($request->payment_type)
            $expertise->payment_type = $request->payment_type;
        if ($request->plus_kart_id && $request->payment_type == 'plus_kart')
            $expertise->plus_kart_id = $request->plus_kart_id;
        else
            $expertise->plus_kart_id = null;
        if ($request->plus_card_payment_type && $request->payment_type == 'plus_kart')
            $expertise->plus_card_payment_type = $request->plus_card_payment_type;
        else
            $expertise->plus_card_payment_type = null;
//        $expertise->save();
        $expertise->save();


        if ($expertise->cari_id < 1)
            return back()->with('error','Ruhsat Sahibi Seçilmedi!');
        elseif ($expertise->alici_id < 1)
            return back()->with('error','Alıcı Seçilmedi!');
        elseif ($expertise->satici_id < 1)
            return back()->with('error','Satıcı Seçilmedi!');
        elseif ($expertise->car_id < 1)
            return back()->with('error','Araç Seçilmedi!');


        $oldStocks = ExpertiseStock::where('expertise_id',$expertise->id)->get();
        foreach ($oldStocks as $oldStock){
            $oldStock->delete();
            logRecord(
                "delete_expertise_stock",
                $authUser->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydının ".$oldStock->getStock->ad." hizmetini sildi.",
                $oldStock->id
            );
        }
        if ($request->stock_id){
            $hizmetAdet = 0;
            foreach ($request->stock_id as $key => $item){
                if ($item > 0){

                    $expertiseStock = ExpertiseStock::where('expertise_id',$expertise->id)
                        ->where('stock_id',$request->stock_id[$key])
                        ->first();

                    if(!$expertiseStock){
                        $expertiseStock = new ExpertiseStock();
                    }

                    $kampanyaID = isset($request->campaign_id[$key]) && !empty($request->campaign_id[$key]) ? (int)$request->campaign_id[$key] : 0;
                    $stock = Stock::where('id',$request->stock_id[$key])->first();
                    if(!empty($expertise->id)){
                        $listeFiyati = $stock->getPrices->where('campaign_id',$kampanyaID > 0 ? $kampanyaID : 0)->first() ? $stock->getPrices->where('campaign_id',$kampanyaID > 0 ? $kampanyaID : 0)->first()->kdv_dahil_fiyat : 0;
                        if ($request->contract_no && $request->contract_code){
                            $contract = Contract::where('no',$request->contract_no)->get();
                            if (!$contract)
                                return back()->with('error','Sözleşme Bulunamadı!');

                            $contractCode = ContractCode::whereIn('contract_id',$contract->pluck('id')->toArray())
                                ->where('code',$request->contract_code)->first();
                            if (!$contractCode)
                                return back()->with('error','Sözleşme Bulunamadı!');

                            $contract = $contractCode->getContract;

                            $contractStock = ContractStock::where('contract_id',$contract->id)
                                ->where('stock_id',$request->stock_id[$key])
                                ->where('type',$contractCode->type)
                                ->first();
                            if (!$contractStock){
                                $contractStock = ContractStock::where('contract_id',$contract->id)
                                    ->where('stock_id',$request->stock_id[$key])
                                    ->where('type','tumu')
                                    ->first();
                                if (!$contractStock)
                                    return back()->with('error','Sözleşme Stoğu Bulunamadı!');
                            }

                            $hizmetTutari = $contractStock->price;
                        }else{
                            $hizmetTutari = $listeFiyati - (float)$request->iskonto_amount[$key];
                        }
                        $expertiseStock->expertise_id = $expertise->id;
                        $expertiseStock->stock_id = $request->stock_id[$key];
                        $expertiseStock->campaign_id = isset($request->campaign_id[$key]) && !empty($request->campaign_id[$key]) ? (int)$request->campaign_id[$key] : 0;
                        $expertiseStock->sorgu_hizmeti = $stock->sorgu_hizmeti;
                        $expertiseStock->yol_yardimi = $stock->yol_yardimi;
                        $expertiseStock->iskonto_amount = (float)$request->iskonto_amount[$key]; // Burada dizi olarak gelen değerler doğru şekilde eşleniyor
                        $expertiseStock->hizmet_tutari = $hizmetTutari;
                        $expertiseStock->liste_fiyati = $listeFiyati;
                        $expertiseStock->save();
                        $hizmetAdet++;
                        if ($stock->sorgu_hizmeti){
                            $checkQueryLog = QueryLog::where('uuid',$expertise->uuid)->exists();
                            if (!$checkQueryLog){
                                $queryLog = new QueryLog();
                                $queryLog->uuid = $expertise->uuid;
                                $queryLog->user_id = \auth()->id();
                                $queryLog->save();
                            }
                        }
                    }

                    logRecord(
                        "add_expertise_stock_for_expertise",
                        $authUser->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydına ".$expertiseStock->ad . " hizmeti ekledi.",
                        $expertiseStock->id
                    );
                }
            }
        }else{
            return back()->with('error','Hizmet Seçilmedi!');
        }
        if ($hizmetAdet == 0){
            return back()->with('error','Hizmet Seçilmedi!');
        }


        if ($request->payment_type == 'plus_kart'){
            if ((int)$request->plus_kart_id < 1){
                return redirect()->route('expertises.create')->with('error','Plus Card Bulunamadı!');
            }

            $plusCard = PlusCard::find($request->plus_kart_id);
            $balance = $plusCard->getTotalBalanceStock($request->stock_id[0],$expertise->branch_id);
            if($request->plus_card_payment_type == "puan" && $balance['points'] > 0){
                $stock_id = $request->stock_id[0];


                $last_puan = PlusCardCrediAndPuanAdd::where('card_id',$plusCard->id)
                    ->where('puan_branche_id',$expertise->branch_id)
                    ->orderBy('id','asc')
                    ->get()->map(function($item) use ($stock_id) {
                        if ($item->valid_date == null || $item->valid_date >= date('Y-m-d')) {
                            if($item->getStockPivot->where('stock_id',$stock_id)->count() > 0){

                                return $item;
                            }
                        }
                    });


                if(!empty($last_puan)){
                    if (isset($request->campaign_id) && is_array($request->campaign_id) && $request->campaign_id[0] > 0)
                        return redirect()->route('expertises.create')->with('error','Kampanyada Plus Card Kullanılamaz!');

                    $odeme_id = 0;
                    $unit_price = 0;
                    foreach($last_puan as $lp){
                        if(!empty($lp->id)){
                            $lps = ExpertisePayment::where('type','plus_kart')
                                ->where('plus_card_id',$plusCard->id)
                                ->where('plus_card_odeme_id',$lp->id)
                                ->count();
                            $removeCount = PlusCardCrediAndPuanRemove::where('plus_card_credi_and_puan_add_id',$lp->id)->sum('amount') ?? 0;
                            if(($lps + $removeCount) < $lp->puan){
                                $odeme_id = $lp->id;
                                $unit_price = $lp->unit_price;
                                break;
                            }
                        }
                    }
                    if($odeme_id != 0){
                        ExpertisePayment::where('expertise_id',$expertise->id)->delete();
                        $expertisePayment = new ExpertisePayment();
                        $expertisePayment->expertise_id = $expertise->id;
                        $expertisePayment->case_id = 0;
                        $expertisePayment->amount = $unit_price ?? 0;
                        $expertisePayment->type = 'plus_kart';
                        $expertisePayment->plus_card_id = $plusCard->id;
                        $expertisePayment->plus_card_odeme_id = $odeme_id;
                        $expertisePayment->save();
                    }else{
                        return redirect()->route('expertises.create')->with('error','Plus Card Puan Bakiye Yetersiz!');
                    }

                }else{
                    return redirect()->route('expertises.create')->with('error','Plus Card Puan Yükleme Bulunamadı!');
                }
            }
            if($request->plus_card_payment_type == "kredi" && $balance['credits'] > 0){
                $stock_id = $request->stock_id[0];
                $last_credits = PlusCardCrediAndPuanAdd::
//                    search($plusCard->id)
                    where('card_id',$plusCard->id)
//                    ->orderBy('id','asc')
                    ->orderBy('unit_price', 'asc')
                    ->get()->map(function($item) use ($stock_id) {
                        if ($item->valid_date == null || $item->valid_date >= date('Y-m-d')) {
                            if($item->getStockPivot->where('stock_id',$stock_id)->count() > 0){
                                return $item;
                            }
                        }elseif ($item->definitions_id > 0){
                            $definition = PlusCardsDefinitions::where('id',$item->definitions_id)->first();
                            if ($definition && $definition->unit_price == $item->unit_price){
                                if($item->getStockPivot->where('stock_id',$stock_id)->count() > 0){
                                    return $item;
                                }
                            }
                        }
                    });
                if(!empty($last_credits)){
                    if (isset($request->campaign_id) && is_array($request->campaign_id) && $request->campaign_id[0] > 0)
                        return redirect()->route('expertises.create')->with('error','Kampanyada Plus Card Kullanılamaz!');

                    $odeme_id = 0;
                    $unit_price = 0;
                    foreach($last_credits as $lp){
                        if(!empty($lp->id)){
                            $lps = ExpertisePayment::where('type','plus_kart')
                                ->where('plus_card_id',$plusCard->id)
                                ->where('plus_card_odeme_id',$lp->id)
                                ->count();
                            $removes = PlusCardCrediAndPuanRemove::where('plus_card_credi_and_puan_add_id',$lp->id)->sum('amount') ?? 0;
                            $lps += $removes;
                            if($lps < $lp->credi){
                                $odeme_id = $lp->id;
                                $unit_price = $lp->unit_price;
                                break;
                            }
                        }

                    }

                    if($odeme_id != 0){
                        ExpertisePayment::where('expertise_id',$expertise->id)->delete();
                        $expertisePayment = new ExpertisePayment();
                        $expertisePayment->expertise_id = $expertise->id;
                        $expertisePayment->case_id = 0;
                        $expertisePayment->amount = $unit_price ?? 0;
                        $expertisePayment->type = 'plus_kart';
                        $expertisePayment->plus_card_id = $plusCard->id;
                        $expertisePayment->plus_card_odeme_id = $odeme_id;
                        $expertisePayment->save();
                    }else{
                        return redirect()->route('expertises.create')->with('error','Plus Card Kredi Yüklemesi Yetersiz!');
                    }

                }else{
                    return redirect()->route('expertises.create')->with('error','Plus Card Kredi Yüklemesi Bulunamadı!');
                }
            }

            $telephone = !empty($plusCard->getCustomer->telefon) ? $plusCard->getCustomer->telefon : $plusCard->getCustomer->cep;
            $settings = Setting::first();
            if ($settings->netgsm_active == 1 && !empty($telephone)){
                if (substr($telephone, 0, 1) === '0') {
                    $telephone = substr($telephone, 1);
                }
                $car_setting = Car::find($request->car_id);
                $message1 = "Sayın; ".$plusCard->getCustomer->unvan." - ".$car_setting->plaka." Plakalı Aracınıza bugun ";
                $message2 = $authUser->getBranch->kisa_ad." bayimizde PlusCard uzerinde harcama yapılmıstır.";
                $return1 = sendSmsOTP($settings->netgsm_usercode,$settings->netgsm_password,$settings->netgsm_msgheader2,$message1,$telephone);
                $return2 = sendSmsOTP($settings->netgsm_usercode,$settings->netgsm_password,$settings->netgsm_msgheader2,$message2,$telephone);
            }
        }
        elseif ($request->payment_type == 'sozlesme'){
            $check = ContractController::contractChecker($request->contract_no,$request->contract_code,$request->branch_id,$request->payment_id,$request->car_id);

            if ($check['success'] === false)
                return redirect()->route('expertises.create')->with('error',$check['message']);

            $contract = $check['contract'];
            $contractCode = $check['contractCode'];

            $contractCode->invoice_customer_id = $contractCode->type == 'alici' ? ((int)$request->alici_id ?? (int)$request->cari_id) : ($contractCode->type == 'satici' ? ((int)$request->satici_id ?? (int)$request->cari_id) : $contract->customer_id);
            $contractCode->used = 1;
            $contractCode->save();

            ExpertisePayment::where('expertise_id',$expertise->id)->where('type','!=','sozlesme')->delete();
            foreach ($request->stock_id as $stockID){
                $contractStock = ContractStock::where(['contract_id'=>$contract->id,'stock_id'=>$stockID,'type'=>$contractCode->type])->first();
                if (!$contractStock){
                    $contractStock = ContractStock::where(['contract_id'=>$contract->id,'stock_id'=>$stockID,'type'=>'tumu'])->first();
                }

                if ($contractStock){
                    $expertisePayment = new ExpertisePayment();
                    $expertisePayment->expertise_id = $expertise->id;
                    $expertisePayment->case_id = 0;
                    $expertisePayment->amount = $contractStock->price;
                    $expertisePayment->type = 'sozlesme';
                    $expertisePayment->payment_code = $request->contract_no;
                    $expertisePayment->payment_detail = $request->contract_code;
                    $expertisePayment->save();
                }
            }
        }
        else{
            if ($request->odeme_tip){

                $odemeAdet = 0;
                $payments_id = array();
                foreach ($request->odeme_tip as $key => $tip){
                    $temizVeri = str_replace('₺', '', $request->odeme_tutar[$key]);
                    $temizVeri = str_replace('.', '', $temizVeri);
                    $temizVeri = str_replace(',','.', $temizVeri);
                    if($temizVeri > 0){
                        $expertisePayment = ExpertisePayment::where('expertise_id',$expertise->id)
                            ->where('type',$request->odeme_tip[$key])
                            ->first();
                        if(empty($expertisePayment)){
                            $expertisePayment = new ExpertisePayment();
                        }

                        $expertisePayment->expertise_id = $expertise->id;
                        //$expertisePayment->case_id = $request->odeme_hesap[$key];
                        if (is_array($request->odeme_hesap) && isset($request->odeme_hesap[$key])) {
                            $expertisePayment->case_id = $request->odeme_hesap[$key];
                        } else {
                            $expertisePayment->case_id = null; // Veya başka bir varsayılan değer
                        }

                        if (!is_null($request->contract_no) && !is_null($request->contract_code)){
                            $check = ContractController::contractChecker($request->contract_no,$request->contract_code,$request->branch_id,$request->payment_id,$request->car_id);

                            if ($check['success'] === false)
                                return redirect()->route('expertises.create')->with('error',$check['message']);

                            $contract = $check['contract'];
                            $contractCode = $check['contractCode'];

                            $contractCode->invoice_customer_id = $contractCode->type == 'alici' ? ((int)$request->alici_id ?? (int)$request->cari_id) : ($contractCode->type == 'satici' ? ((int)$request->satici_id ?? (int)$request->cari_id) : $contract->customer_id);
                            $contractCode->used = 1;
                            $contractCode->save();
                        }
                        $expertisePayment->amount = (float)$temizVeri;
                        $expertisePayment->type = $request->odeme_tip[$key];
                        $expertisePayment->payment_code = $request->contract_no;
                        $expertisePayment->payment_detail = $request->contract_code;
                        $expertisePayment->save();

                        $payments_id[] = $expertisePayment->id;
                        $odemeAdet++;
                        logRecord(
                            "add_expertise_payment_for_expertise",
                            $authUser->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydına ".(float)str_replace(',','.',$request->odeme_tutar[$key]). "₺ ödeme ekledi.",
                            $expertisePayment->id
                        );
                    }

                }

                //$delete_expertise_payment = ExpertisePayment::whereNotIn('id',$payments_id)->delete();
                $expertisePayment = ExpertisePayment::where('expertise_id',$expertise->id)
                    ->count();
                if($expertisePayment == 0){
                    return back()->with('error','Ödeme Seçilmedi!');
                }
                if ($odemeAdet == 0){
                    return back()->with('error','Ödeme Seçilmedi!');
                }
            }else{
                return back()->with('error','Ödeme Seçilmedi!');
            }
        }


        $expertise->status = 1;
        $expertise->manuel_save = 1;
        $expertise->save();


        if(!empty($request->km)){
            $car_update = Car::find($request->car_id);
            if(!empty($car_update)){
                $car_update->km = $request->km;
                $car_update->km_type = $request->km_type;
                $car_update->save();
            }
        }

        logRecord(
            "add_expertise",
            $authUser->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşteri için ekspertiz kaydı ekledi.",
            $expertise->id
        );

        if ($request->plus_card_sms_verified == 1){
            $expertisePlusCardSmsVerification = new ExpertisePlusCardSmsVerification();
            $expertisePlusCardSmsVerification->expertise_id = $expertise->id;
            $expertisePlusCardSmsVerification->cep = $request->plus_card_sms_cep;
            $expertisePlusCardSmsVerification->telefon = $request->plus_card_sms_telefon;
            $expertisePlusCardSmsVerification->code_telefon = $request->plus_card_sms_telefon_code;
            $expertisePlusCardSmsVerification->code_cep = $request->plus_card_sms_cep_code;
            $expertisePlusCardSmsVerification->type = $request->plus_card_sms_verified_type;
            $expertisePlusCardSmsVerification->save();
        }

        if ($request->save_type == 'normal')
            return redirect()->route('expertises.index')->with('success','Başarılı');
        else
            return redirect()->route('expertises.index',['sozlesme'=>$expertise->uuid])->with('success','Başarılı');
//            return redirect()->route('aliciSozlesme',$expertise->uuid)->with('success','Başarılı');
    }

    /**
     * Display the specified resource.
     */
    public function show(Expertise $expertise)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($uuid)
    {
        /** @var \App\Models\User $authUser */
        $authUser = \auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['edit_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $newDB = Expertise::where('uuid',$uuid)->first();
        $is_close_redirect = false;
        if ($newDB){
            $satici = Customer::where('id',$newDB->satici_id)->select('id','unvan','ad','soyad','cari_kod','telefon','cep')->first();
            $saticiCustomer = $satici;
            $alici = Customer::where('id',$newDB->alici_id)->select('id','unvan','ad','soyad','cari_kod','telefon','cep')->first();
            $aliciCustomer = $alici;
            $cari = Customer::where('id',$newDB->cari_id)->select('id','unvan','ad','soyad','cari_kod','telefon','cep')->first();
            $ruhsatCustomer = $cari;
            $arac = Car::where('id',$newDB->car_id)->select('*')->first();
            $car = $arac;
            $stoklar = [];

            $islemler = [
                'arac' => $newDB->arac_kontrol,
                'fren' => $newDB->fren_kontrol,
                'kaporta' => $newDB->kaporta_kontrol,
                'diagnostic' => $newDB->diagnostic_kontrol,
                'lastik' => $newDB->lastik_jant_kontrol,
                'motor' => $newDB->alt_motor_kontrol,
                'ic' => $newDB->ic_kontrol,
                'komponent' => $newDB->komponent_kontrol,
                'co2'=>$newDB->co2_kontrol,
            ];

            foreach ($newDB->getStocks as $stok){
                $stoklar[] = [
                    'ad' => $stok->getStock->ad,
                    'campaign_id' => $stok->campaign_id,
                    'sorgu_hizmet' => $stok->sorgu_hizmet,
                    'yol_yardimi' => $stok->yol_yardimi,
                    'iskonto_amount' => $stok->iskonto_amount,
                    'hizmet_tutari' => $stok->hizmet_tutari,
                    'liste_fiyati' => $stok->liste_fiyati,
                ];
            }

            // Müşteriye ait promosyon kodunu al
            $customerPromoCode = null;

            if ($newDB->getStockhasOne && $newDB->getStockhasOne->campaign_id == 265) {
                $customerPromoCode = \App\Models\PromotionCode::where('customer_id', $aliciCustomer->id)
                    ->where('is_used', 1)
                    ->orderByDesc('created_at')
                    ->first();
            }

            $odemeler = [];
            foreach ($newDB->getPayments as $payment){
                $odemeler[] = [
                    'type' => $payment->type,
                    'amount' => $payment->amount,
                    'plus_card_id' => $payment->getPlusCards,
                    'plus_card_odeme_id' => $payment->getPlusCardOdeme,
                ];
            }
            $is_close_redirect = $newDB->isCompleteDate();
            $unlock_tabs = array();
            foreach($newDB->getStocks as $gt){
                if($gt->getStock->car == 1 && !in_array('car', $unlock_tabs)){
                    $unlock_tabs[] = 'car';
                }
                if($gt->getStock->brake == 1 && !in_array('brake', $unlock_tabs)){
                    $unlock_tabs[] = 'brake';
                }
                if($gt->getStock->bodywork == 1 && !in_array('bodywork', $unlock_tabs)){
                    $unlock_tabs[] = 'bodywork';
                }
                if($gt->getStock->internal_control == 1 && !in_array('internal_control', $unlock_tabs)){
                    $unlock_tabs[] = 'internal_control';
                }
                if($gt->getStock->tire_and_rim == 1 && !in_array('tire_and_rim', $unlock_tabs)){
                    $unlock_tabs[] = 'tire_and_rim';
                }
                if($gt->getStock->sub_control_and_engine == 1 && !in_array('sub_control_and_engine', $unlock_tabs)){
                    $unlock_tabs[] = 'sub_control_and_engine';
                }
                if($gt->getStock->component == 1 && !in_array('component', $unlock_tabs)){
                    $unlock_tabs[] = 'component';
                }
                if($gt->getStock->diagnostic == 1 && !in_array('diagnostic', $unlock_tabs)){
                    $unlock_tabs[] = 'diagnostic';
                }
                if(($gt->getStock->co2 == 1 && !in_array('co2', $unlock_tabs)) || $gt->getStock->id == 209 || $gt->campaign_id == 261 || $gt->campaign_id == 267){
                    $unlock_tabs[] = "co2";
                }
                if($gt->getStock->sorgu_hizmeti == 1){
                    $unlock_tabs[] = "query";
                }
            }
            $expertise = [
                'is_new' => 1,
                'cariKod' => $cari->cari_kod ?? '',
                'cariUnvan' => $cari->fullName ?? '',
                'nereden_ulastiniz' => $newDB->nereden_ulastiniz ?? '',
                'saticiKod' => $satici->cari_kod ?? '',
                'saticiUnvan' => $satici->fullName ?? '',
                'aliciKod' => $alici->cari_kod ?? '',
                'aliciUnvan' => $alici->fullName ?? '',
                'plaka' => !empty($arac->plaka) ? $arac->plaka:'',
                'sase_no' => $newDB->sase_no ? $newDB->sase_no : ( !empty($arac->sase_no) ? $arac->sase_no:''),
                'belge_ozel_kodu' => $newDB->belge_ozel_kodu,
                'belge_tarihi' => $newDB->belge_tarihi,
                'cikis_tarihi' => $newDB->cikis_tarihi,
                'belge_no' => $newDB->belge_no,
                'sigorta_teklif_ver' => $newDB->sigorta_teklif_ver == null || $newDB->sigorta_teklif_ver == 0 ? 0 : 1,
                'yayin_yasagi' => $newDB->yayin_yasagi == 1 ? 1 : 0,
                'kayitBranch' => $newDB->getRegisterBranch?->kisa_ad,
                'branch' => $newDB->getBranch?->kisa_ad,
                'status' => $newDB->status,
                'getStocks' => $stoklar,
                'getPayments' => $odemeler,
                'audio_url' => '/storage/'.$newDB->audio_url,
                'uuid' => $newDB->uuid,
                'arac_kontrol' => $newDB->arac_kontrol,
                'fren_kontrol' => $newDB->fren_kontrol,
                'kaporta_kontrol' => $newDB->kaporta_kontrol,
                'diagnostic_kontrol' => $newDB->diagnostic_kontrol,
                'ic_kontrol' => $newDB->ic_kontrol,
                'lastik_jant_kontrol' => $newDB->lastik_jant_kontrol,
                'alt_motor_kontrol' => $newDB->alt_motor_kontrol,
                'komponent_kontrol' => $newDB->komponent_kontrol,
                'unlock_tabs' => $unlock_tabs,
                'customerPromoCode' => $customerPromoCode,
            ];
        }
        elseif (!env('APP_LOCAL')){
            if (!$newDB)
                $oldDB = DB::connection('mysql2')->table('T400_SRVBASLIK')->where('T400_UQ',$uuid)->first();

            if (!$oldDB)
                return redirect()->route('expertises.index')->with('error','Kayıt Bulunamadı!');
            $is_close_redirect = true;
            $registerBranch = DB::connection('mysql2')->table('T103_SUBELER')->where('T103_kod',$oldDB->T400_kayit_yeri)->select('T103_kisaadi')->first();
            $branch = DB::connection('mysql2')->table('T103_SUBELER')->where('T103_kod',$oldDB->T400_subekodu)->select('T103_kisaadi')->first();
            $alici = DB::connection('mysql2')->table('T201_CARILER')->where('hesap_UQ',$oldDB->T400_musteri_UQ)->select('unvan','adi','soyadi')->first();
            $cari = DB::connection('mysql2')->table('T201_CARILER')->where('hesap_UQ',$oldDB->T400_satici_UQ)->select('unvan','adi','soyadi')->first();
            $arac = DB::connection('mysql2')->table('T202_ARACLAR')->where('T202_UQ',$oldDB->T400_arac_UQ)->select('T202_saseno','T202_plakano','T202_modelyili','T202_motor_cekis')->first();
            $stok = DB::connection('mysql2')->table('T203_STOKLAR')->where('T203_UQ',$oldDB->T400_hizmet_UQ)->select('T203_stokadi')->first();

            $stoklar[] = [
                'ad' => $stok->T203_stokadi,
                'campaign_id' => 0,
                'sorgu_hizmet' => $oldDB->T400_hasar_sorgulama == 1 ? 1 : 0,
                'yol_yardimi' => $oldDB->T400_yol_yardim == 1 ? 1 : 0,
                'hizmet_tutari' => $oldDB->T400_tltahsilattutari,
                'liste_fiyati' => $oldDB->T400_tlsatistutari,
            ];

            $odemeler = [];
            $islemler = [
                'arac' => 1,
                'fren' => 1,
                'kaporta' => 1,
                'diagnostic' => 1,
                'lastik' => 1,
                'motor' => 1,
                'ic' => 1,
                'komponent' => 1,
                'co2'=>1,
            ];
            $expertise = [
                'is_new' => 0,
                'cariKod' => $cari->kisakod ?? '',
                'cariUnvan' => $cari ? $cari->unvan ?? ($cari->adi . ' ' . $cari->soyadi ?? '') : '',
                'nereden_ulastiniz' => $cari->T400_reklamulasimyeri ?? '',
                'saticiKod' => $cari->kisakod ?? '',
                'saticiUnvan' => $cari ? $cari->unvan ?? ($cari->adi . ' ' . $cari->soyadi ?? '') : '',
                'aliciKod' => $alici->kisakod ?? '',
                'aliciUnvan' => $alici ? $alici->unvan ?? ($alici->adi . ' ' . $alici->soyadi ?? '') : '',
                'plaka' => $arac->T202_plakano,
                'sase_no' => $arac->T202_saseno,
                'belge_ozel_kodu' => $oldDB->T400_belgeozelkodu,
                'belge_tarihi' => $oldDB->T400_belgetarihi,
                'cikis_tarihi' => $oldDB->T400_cikistarihi,
                'belge_no' => $oldDB->T400_belgeno,
                'sigorta_teklif_ver' => $oldDB->T400_sigorta_teklifi == null || $oldDB->T400_sigorta_teklifi == 0 ? 'Hayır' : 'Evet',
                'yayin_yasagi' => $oldDB->T400_sitedeyayinyasak == 1 ? 'Evet' : 'Hayır',
                'kayitBranch' => $registerBranch->T103_kisaadi ?? ($registerBranch->T103_unvan ?? ''),
                'branch' => $branch->T103_kisaadi,
                'status' => 1,
                'getStocks' => $stoklar,
                'getPayments' =>$odemeler,
                'audio_url' => null,
                'uuid' => $oldDB->T400_UQ
            ];
        }else{
            return redirect()->route('expertises.index')->with('error','Kayıt Bulunamadı!');
        }

        if($authUser->type != 'admin' && $is_close_redirect){
            return redirect()->back()->with('error','Yetkiniz Yok!');
        }


        if ($authUser->department == 'arac_kontrol')
            return redirect()->route('expertise_details',['type'=>'car','expertise_id'=>$expertise['uuid']]);
        elseif ($authUser->department == 'fren_kontrol')
            return redirect()->route('expertise_details',['type'=>'brake','expertise_id'=>$expertise['uuid']]);
        elseif ($authUser->department == 'kaporta_kontrol')
            return redirect()->route('expertise_details',['type'=>'bodywork','expertise_id'=>$expertise['uuid']]);
        elseif ($authUser->department == 'diagnostic_kontrol')
            return redirect()->route('expertise_details',['type'=>'diagnostic','expertise_id'=>$expertise['uuid']]);
        elseif ($authUser->department == 'ic_kontrol')
            return redirect()->route('expertise_details',['type'=>'internal_control','expertise_id'=>$expertise['uuid']]);
        elseif ($authUser->department == 'lastik_jant_kontrol')
            return redirect()->route('expertise_details',['type'=>'tire_and_rim','expertise_id'=>$expertise['uuid']]);
        elseif ($authUser->department == 'alt_motor_kontrol')
            return redirect()->route('expertise_details',['type'=>'sub_controls_and_engine','expertise_id'=>$expertise['uuid']]);
        elseif ($authUser->department == 'komponent_kontrol')
            return redirect()->route('expertise_details',['type'=>'component','expertise_id'=>$expertise['uuid']]);
        elseif ($authUser->department == 'co2')
            return redirect()->route('expertise_details',['type'=>'co2','expertise_id'=>$expertise['uuid']]);
        $stocks = Stock::where('status',1)->where('show_on_expertise',1)->orderBy('rank','asc')->get();
        $campaigns = Campaign::where('status',1)->get();
        $branches = $authUser->activeBranches();

        $expertise['islemler'] = $islemler;

        $plusCardSmsCodeCep = 'UMR-'.rand(10,99).'-'.rand(10,99);
        $plusCardSmsCodeTelefon = 'UMR-'.rand(10,99).'-'.rand(10,99);


        return view('pages.expertise.admin_edit',compact([
            'stocks','branches',
            'campaigns','expertise',
            'expertise','newDB',
            'saticiCustomer','ruhsatCustomer','aliciCustomer','car',
            'plusCardSmsCodeCep','plusCardSmsCodeTelefon'
        ]));


        if ($canSeeDetailed){
            return view('pages.expertise.admin_edit',compact([
                'stocks','branches',
                'campaigns','expertise',
                'genelPersoneller','expertise','newDB',
                'saticiCustomer','ruhsatCustomer','aliciCustomer','car'
            ]));
        }

        else
            return view('pages.expertise.admin_edit',compact(['stocks','branches','campaigns','newDB','genelPersoneller','expertise']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Expertise $expertise)
    {
        $authUser = auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['edit_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $authUserBranchIds = $authUser->getBranchIds();
        $checkNewOrOld = Expertise::where('uuid',$request->uuid)->first();
        if (!$checkNewOrOld)
            return back()->with('error','Eski Kayıtlar Düzenlenemez!');

        if ($authUser->type == 'admin'){
            $getCari = $checkNewOrOld->getCari;
            $logText = $authUser->name . " adlı kullanıcı ".$getCari?->unvan." adlı müşterinin ekspertiz kaydının ";

            if ($request->km && $checkNewOrOld->km != (int)$request->km){
                $logText .= "km'sini ". $checkNewOrOld->km."=>". (int)$request->km ." olarak güncelledi.";
                $checkNewOrOld->km = (int)$request->km;
            }


            if ($request->cari_id && $checkNewOrOld->cari_id != $request->cari_id){
                $query = User::where('id',$request->cari_id)->first();
                $logText .= "ruhsat sahibini ". $getCari?->unvan."=>".(!is_null($query) ? $query->name : '-') ." olarak güncelledi.";
                $checkNewOrOld->cari_id = $request->cari_id;
            }

            if (isset($request->nereden_ulastiniz) && $request->nereden_ulastiniz != $checkNewOrOld->nereden_ulastiniz){
                $logText .= "nereden ulaştınız seçeneğini $checkNewOrOld->nereden_ulastiniz => $request->nereden_ulastiniz olarak güncelledi.";
                $checkNewOrOld->nereden_ulastiniz = $request->nereden_ulastiniz;
            }

            if ($request->satici_id && $checkNewOrOld->satici_id != $request->satici_id){
                $query = User::where('id',$request->satici_id)->first();
                $logText .= " Ruhsat sahibini ". $checkNewOrOld->getSatici?->unvan."=>".(!is_null($query) ? $query->name : '-') ." olarak güncelledi.";
                $checkNewOrOld->satici_id = $request->satici_id;
            }

            if ($request->alici_id && $checkNewOrOld->alici_id != $request->alici_id){
                $query = User::where('id',$request->alici_id)->first();
                $logText .= " Alıcısını ". $checkNewOrOld->getAlici?->unvan."=>".(!is_null($query) ? $query->name : '-') ." olarak güncelledi.";
                $checkNewOrOld->alici_id = $request->alici_id;
            }

            if ($request->car_id && $checkNewOrOld->car_id != $request->car_id){
                $query = Car::where('id',$request->car_id)->first();
                $logText .= " Aracını ". (!empty($checkNewOrOld->getCar) ? $checkNewOrOld->getCar?->plaka : '')."=>".(!is_null($query) ? $query->plaka : '-') ." plakalı araç olarak güncelledi.";
                $checkNewOrOld->car_id = $request->car_id;
            }
            if (isset($request->belge_tarihi) && $checkNewOrOld->belge_tarihi != $request->belge_tarihi){
                $logText .= " Belge tarihini $checkNewOrOld->belge_tarihi => $request->belge_tarihi olarak güncelledi.";
                $checkNewOrOld->belge_tarihi = $request->belge_tarihi;
            }


            if (isset($request->belge_no) && $checkNewOrOld->belge_no != $request->belge_no){
                $logText .= " Belge nosunu $checkNewOrOld->belge_no => $request->belge_no olarak güncelledi.";
                $checkNewOrOld->belge_no = $request->belge_no;
            }

            if (isset($request->payment_type) && $checkNewOrOld->payment_type != $request->payment_type){
                $logText .= " Ödeme tipini $checkNewOrOld->payment_type => $request->payment_type olarak güncelledi.";
                $checkNewOrOld->payment_type = $request->payment_type;
            }


            if (isset($request->belge_ozel_kodu) && $checkNewOrOld->belge_ozel_kodu != $request->belge_ozel_kodu){
                $logText .= " Belge özel kodunu $checkNewOrOld->belge_ozel_kodu => $request->belge_ozel_kodu olarak güncelledi.";
                $checkNewOrOld->belge_ozel_kodu = $request->belge_ozel_kodu;
            }

            if (isset($request->km) && $checkNewOrOld->km != $request->km){
                $logText .= " Kilometresini $checkNewOrOld->km => $request->km olarak güncelledi.";
                $checkNewOrOld->km = $request->km;

                $car = Car::where('id',$request->car_id)->first();
                if($car){
                    logRecord(
                        "edit_car",
                        $authUser->name . " adlı kullanıcı ".$car->plaka." plakalı aracın kilometresini $car->km => $request->km olarak güncelledi.",
                        $car->id
                    );
                    $car->km = $request->km;
                    $car->save();
                }
            }

            if (isset($request->sigorta_teklif_ver) && $checkNewOrOld->sigorta_teklif_ver != $request->sigorta_teklif_ver){
                $logText .= " Sigorta teklif ver seçeneğini $checkNewOrOld->sigorta_teklif_ver => $request->sigorta_teklif_ver olarak güncelledi.";
                $checkNewOrOld->sigorta_teklif_ver = $request->sigorta_teklif_ver;
            }


            if (isset($request->yayin_yasagi) && $checkNewOrOld->yayin_yasagi != (int)$request->yayin_yasagi){
                $logText .= " Yayın yasağını $checkNewOrOld->yayin_yasagi => $request->yayin_yasagi olarak güncelledi.";
                $checkNewOrOld->yayin_yasagi = (int)$request->yayin_yasagi;
            }
            if (isset($request->kayit_branch_id) && $checkNewOrOld->kayit_branch_id != $request->kayit_branch_id){
                $logText .= " Kayıt şube id'sini $checkNewOrOld->kayit_branch_id => $request->kayit_branch_id olarak güncelledi.";
                $checkNewOrOld->kayit_branch_id = $request->kayit_branch_id;
            }
            if (isset($request->branch_id) && $checkNewOrOld->branch_id != $request->branch_id){
                $logText .= " İşlem şube id'sini $checkNewOrOld->branch_id => $request->branch_id olarak güncelledi.";
                $checkNewOrOld->branch_id = $request->branch_id;
            }
            if (isset($request->status) && $checkNewOrOld->status != $request->status){
                $logText .= "kaydının durumunu $checkNewOrOld->status => $request->status olarak güncelledi.";
                $checkNewOrOld->status = $request->status;
            }
            if (isset($request->cikis_tarihi) && $checkNewOrOld->cikis_tarihi != $request->cikis_tarihi){
                $logText .= " Çıkış tarihini $checkNewOrOld->cikis_tarihi => $request->cikis_tarihi olarak güncelledi.";

                if (!$authUser->isAdmin()) {
                    $checkNewOrOld->cikis_tarihi = $request->cikis_tarihi;
                }
            }

            if (isset($request->audio_url) && $checkNewOrOld->audio_url != $request->audio_url){
                $logText .= " Ses kayıt yolunu $checkNewOrOld->audio_url => $request->audio_url olarak güncelledi.";
                $checkNewOrOld->audio_url = $request->audio_url;
            }
            $checkNewOrOld->save();

            logRecord(
                "edit_expertise",
                $logText,
                $checkNewOrOld->id
            );

            if ($request->stock_id){
                ExpertiseStock::where('expertise_id',$checkNewOrOld->id)->delete();
                $logText = $authUser->name . " adlı kullanıcı ".$checkNewOrOld->getCari?->unvan." adlı müşterinin ekspertiz kaydının hizmetlerini sildi. ";
                $logText .= $authUser->name . " adlı kullanıcı ".$checkNewOrOld->getCari?->unvan." adlı müşterinin ekspertiz kaydına";

                foreach ($request->stock_id as $key => $item){
                    if ($item > 0){
//                        $expertiseStock = ExpertiseStock::where('expertise_id',$checkNewOrOld->id)
//                            ->where('stock_id',$request->stock_id[$key])
//                            ->first();

//                        if(empty($expertiseStock)){
//                            $expertiseStock = new ExpertiseStock();
//                            $newStock = Stock::where('id',$request->stock_id[$key])->first();
//                            $logText .= $newStock->ad." adlı hizmet ekledi.";
//                        }elseif($expertiseStock->stock_id != $request->stock_id[$key]){
//                            $logText .= "hizmetinin id'sini $expertiseStock->stock_id => ".$request->stock_id[$key]." olarak güncelledi.";
//                        }
                        $expertiseStock = new ExpertiseStock();
                        $newStock = Stock::where('id',$request->stock_id[$key])->first();
                        $logText .= $newStock->ad." adlı hizmet ekledi.";

                        $expertiseStock->expertise_id = $checkNewOrOld->id;
                        $expertiseStock->stock_id = $request->stock_id[$key];
                        $expertiseStock->campaign_id = $request->campaign_id[$key];
                        $expertiseStock->sorgu_hizmeti = $request->sorgu_hizmeti[$key] == 'Var' ? 1 : 0;
                        $expertiseStock->yol_yardimi = $request->yol_yardimi[$key] == 'Var' ? 1 : 0;
                        $expertiseStock->hizmet_tutari = (float)str_replace(',','.',$request->hizmet_tutari[$key]) - (float)str_replace(',','.',$request->iskonto_amount[$key]);
                        $expertiseStock->liste_fiyati = (float)str_replace(',','.',$request->liste_fiyati[$key]);
                        $expertiseStock->iskonto_amount = (float)str_replace(',','.',$request->iskonto_amount[$key]);
                        $expertiseStock->save();


                    }
                }
                if ($logText != $authUser->name . " adlı kullanıcı ".$checkNewOrOld->getCari?->unvan." adlı müşterinin ekspertiz kaydına"){
                    logRecord(
                        "edit_expertise",
                        $logText,
                        $checkNewOrOld->id
                    );
                }
            }



            if ($request->payment_type == 'plus_kart'){
                if ((int)$request->plus_kart_id < 1){
                    return redirect()->route('expertises.create')->with('error','Plus Card Bulunamadı!');
                }

                $plusCard = PlusCard::find($request->plus_kart_id);
                $balance = $plusCard->getTotalBalanceStock($request->stock_id[0],$expertise->branch_id);
                if($request->plus_card_payment_type == "puan" && $balance['points'] > 0){
                    $stock_id = $request->stock_id[0];


                    $last_puan = PlusCardCrediAndPuanAdd::where('card_id',$plusCard->id)
                        ->where('puan_branche_id',$expertise->branch_id)
                        ->orderBy('id','asc')
                        ->get()->map(function($item) use ($stock_id) {
                            if ($item->valid_date == null || $item->valid_date >= date('Y-m-d')) {
                                if($item->getStockPivot->where('stock_id',$stock_id)->count() > 0){
                                    return $item;
                                }
                            }
                        });


                    if(!empty($last_puan)){
                        $odeme_id = 0;
                        $unit_price = 0;
                        foreach($last_puan as $lp){
                            if(!empty($lp->id)){
                                $lps = ExpertisePayment::where('type','plus_kart')
                                    ->where('plus_card_id',$plusCard->id)
                                    ->where('plus_card_odeme_id',$lp->id)
                                    ->count();
                                $puanAdet = $lps;
                                $removeCount = PlusCardCrediAndPuanRemove::where('plus_card_credi_and_puan_add_id',$lp->id)->sum('amount');
                                $puanAdet += $removeCount;
                                $checkOldPayment = ExpertisePayment::where('expertise_id',$expertise->id)->where('plus_card_odeme_id',$lp->id)->first();
                                if ($checkOldPayment)
                                    $puanAdet += 1;
                                if($puanAdet < $lp->puan){
                                    $odeme_id = $lp->id;
                                    $unit_price = $lp->unit_price;
                                    break;
                                }
                            }
                        }
                        if($odeme_id != 0){
                            $oldPayments = ExpertisePayment::where('expertise_id',$expertise->id)->get();
                            foreach ($oldPayments as $oldPayment){
                                $oldPayment->delete();
                                logRecord("delete_expertise_payment",
                                    $authUser->name . " adlı kullanıcı ".$expertise->id." id'li ekspertiz kaydının ".$oldPayment->id." id'li ödeme kaydını kaydını sildi.",
                                    $oldPayment->id
                                );
                            }


                            $expertisePayment = new ExpertisePayment();
                            $expertisePayment->expertise_id = $expertise->id;
                            $expertisePayment->case_id = 0;
                            $expertisePayment->amount = $unit_price ?? 0;
                            $expertisePayment->type = 'plus_kart';
                            $expertisePayment->plus_card_id = $plusCard->id;
                            $expertisePayment->plus_card_odeme_id = $odeme_id;
                            $expertisePayment->save();
                        }else{
                            return redirect()->route('expertises.create')->with('error','Plus Card Puan Bakiye Yetersiz!');
                        }

                    }else{
                        return redirect()->route('expertises.create')->with('error','Plus Card Puan Yükleme Bulunamadı!');
                    }
                }
                if($request->plus_card_payment_type == "kredi" && $balance['credits'] > 0){
                    $stock_id = $request->stock_id[0];
                    $last_credits = PlusCardCrediAndPuanAdd::
//                    search($plusCard->id)
                    where('card_id',$plusCard->id)
                        ->orderBy('id','asc')
                        ->get()->map(function($item) use ($stock_id) {
                            if ($item->valid_date == null || $item->valid_date >= date('Y-m-d')) {
                                if($item->getStockPivot->where('stock_id',$stock_id)->count() > 0){
                                    return $item;
                                }
                            }elseif ($item->definitions_id > 0){
                                $definition = PlusCardsDefinitions::where('id',$item->definitions_id)->first();
                                if ($definition && $definition->unit_price == $item->unit_price){
                                    if($item->getStockPivot->where('stock_id',$stock_id)->count() > 0){
                                        return $item;
                                    }
                                }
                            }
                        });
                    if(!empty($last_credits)){
                        $odeme_id = 0;
                        $unit_price = 0;
                        foreach($last_credits as $lp){
                            if(!empty($lp->id)){
                                $lps = ExpertisePayment::where('type','plus_kart')
                                    ->where('plus_card_id',$plusCard->id)
                                    ->where('plus_card_odeme_id',$lp->id)
                                    ->count();
                                $krediAdet = $lps;
                                $removeCount = PlusCardCrediAndPuanRemove::where('plus_card_credi_and_puan_add_id',$lp->id)->sum('amount');
                                $krediAdet += $removeCount;
                                $checkOldPayment = ExpertisePayment::where('expertise_id',$expertise->id)->where('plus_card_odeme_id',$lp->id)->first();
                                if ($checkOldPayment)
                                    $krediAdet += 1;
                                if($krediAdet < $lp->credi){
                                    $odeme_id = $lp->id;
                                    $unit_price = $lp->unit_price;
                                    break;
                                }
                            }

                        }

                        if($odeme_id != 0){
                            $oldPayments = ExpertisePayment::where('expertise_id',$expertise->id)->get();
                            foreach ($oldPayments as $oldPayment){
                                $oldPayment->delete();
                                logRecord("delete_expertise_payment",
                                    $authUser->name . " adlı kullanıcı ".$expertise->id." id'li ekspertiz kaydının ".$oldPayment->id." id'li ödeme kaydını kaydını sildi.",
                                    $oldPayment->id
                                );
                            }
                            $expertisePayment = new ExpertisePayment();
                            $expertisePayment->expertise_id = $expertise->id;
                            $expertisePayment->case_id = 0;
                            $expertisePayment->amount = $unit_price ?? 0;
                            $expertisePayment->type = 'plus_kart';
                            $expertisePayment->plus_card_id = $plusCard->id;
                            $expertisePayment->plus_card_odeme_id = $odeme_id;
                            $expertisePayment->save();
                        }else{
                            return redirect()->route('expertises.create')->with('error','Plus Card Kredi Yüklemesi Yetersiz!');
                        }

                    }else{
                        return redirect()->route('expertises.create')->with('error','Plus Card Kredi Yüklemesi Bulunamadı!');
                    }
                }

                $telephone = !empty($plusCard->getCustomer->telefon) ? $plusCard->getCustomer->telefon : $plusCard->getCustomer->cep;
                $settings = Setting::first();
                if ($settings->netgsm_active == 1 && !empty($telephone)) {
                    if (substr($telephone, 0, 1) === '0') {
                        $telephone = substr($telephone, 1);
                    }
                    $car_setting = Car::find($request->car_id);

                    // Send SMS
                    $return1 = sendSmsOTP(
                        $settings->netgsm_usercode, 
                        $settings->netgsm_password,
                        $settings->netgsm_msgheader2, 
                        sprintf(
                            "Sayın %s,\n%s bayimizde %s plakalı aracınız için 1 adet PlusCard harcaması yapılmıştır."
                            ($plusCard->getCustomer->fullName ?? 'Müşteri'),
                            ($expertise->getBranch->kisa_ad ?? ''),
                            $car_setting->plaka
                        ),
                        $telephone
                    );
                }
            }
            elseif ($request->payment_type == 'sozlesme'){
                $expertisePayment = ExpertisePayment::where('payment_code',$request->contract_no)
                    ->where('payment_detail',$request->contract_code)
                    ->where('expertise_id',$expertise->id)
                    ->first();
                if (!$expertisePayment){
                    $check = ContractController::contractChecker($request->contract_no,$request->contract_code,$request->branch_id,$request->car_id);

                    if ($check['success'] === false)
                        return redirect()->route('expertises.create')->with('error',$check['message']);

                    $contract = $check['contract'];
                    $contractCode = $check['contractCode'];

                    ExpertisePayment::where('expertise_id',$expertise->id)->delete();
                    foreach ($request->stock_id as $stockID){
                        $contractStock = ContractStock::where(['contract_id'=>$contract->id,'stock_id'=>$stockID,'type'=>$contractCode->type])->first();
                        if ($contractStock){
                            $expertisePayment = new ExpertisePayment();
                            $expertisePayment->expertise_id = $expertise->id;
                            $expertisePayment->case_id = 0;
                            $expertisePayment->amount = $contractStock->price;
                            $expertisePayment->type = 'sozlesme';
                            $expertisePayment->payment_code = $request->contract_no;
                            $expertisePayment->payment_detail = $request->contract_code;
                            $expertisePayment->save();
                        }
                    }

                    $contractCode->invoice_customer_id = $contractCode->type == 'alici' ? ((int)$request->alici_id ?? (int)$request->cari_id) : ($contractCode->type == 'satici' ? ((int)$request->satici_id ?? (int)$request->cari_id) : $contract->customer_id);
                    $contractCode->used = 1;
                    $contractCode->save();
                }
            }
            else{
                if ($request->odeme_tip){
                    $odemeAdet = 0;
                    $ilkKayit = false;
                    foreach ($request->odeme_tip as $key => $tip){
                        $temizVeri = str_replace('₺', '', $request->odeme_tutar[$key]);
                        $temizVeri = str_replace('.', '', $temizVeri);
                        $temizVeri = str_replace(',','.', $temizVeri);



//                        $expertisePayment = ExpertisePayment::where('expertise_id',$expertise->id)
//                            ->where('case_id',$request->odeme_hesap[$key])
//                            ->first();
//                        if(empty($expertisePayment)){
//                            $expertisePayment = new ExpertisePayment();
//                        }
                        $expertisePayment = new ExpertisePayment();
                        $expertisePayment->expertise_id = $expertise->id;
                        $expertisePayment->case_id = $request->odeme_hesap[$key];
                        $expertisePayment->amount = (float)$temizVeri;
                        $expertisePayment->type = $request->odeme_tip[$key];
                        $expertisePayment->payment_code = $request->contract_no;
                        $expertisePayment->payment_detail = $request->contract_code;
                        $expertisePayment->save();
                        if($expertisePayment && $ilkKayit == false)
                            $ilkKayit = $expertisePayment;

                        $odemeAdet++;
                        logRecord(
                            "add_expertise_payment_for_expertise",
                            $authUser->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydına ".(float)str_replace(',','.',$request->odeme_tutar[$key]). "₺ ödeme ekledi.",
                            $expertisePayment->id
                        );
                    }

                    if ($ilkKayit){
                        $oldPayments = ExpertisePayment::where('expertise_id',$expertise->id)->where('created_at','<',$ilkKayit->created_at)->get();
                        foreach ($oldPayments as $oldPayment){
                            $oldPayment->delete();
                            logRecord("delete_expertise_payment",
                                $authUser->name . " adlı kullanıcı ".$expertise->id." id'li ekspertiz kaydının ".$oldPayment->id." id'li ödeme kaydını kaydını sildi.",
                                $oldPayment->id
                            );
                        }

                    }

                    if ($odemeAdet == 0){
                        return back()->with('error','Ödeme Seçilmedi!');
                    }
                }else{
                    return back()->with('error','Ödeme Seçilmedi!');
                }
            }


//            if ($request->odeme_tip){
//                foreach ($request->odeme_tip as $key => $tip){
//                    $temizVeri = str_replace('₺', '', $request->odeme_tutar[$key]);
//                    $temizVeri = str_replace('.', '', $temizVeri);
//                    $temizVeri = str_replace(',','.', $temizVeri);
//                    $expertisePayment = ExpertisePayment::where('expertise_id',$checkNewOrOld->id)
//                        ->where('type',$request->odeme_tip[$key])
////                        ->where('case_id',$request->odeme_hesap[$key])
//                        ->first();
//
//                    if(empty($expertisePayment)){
//                        $expertisePayment = new ExpertisePayment();
//                    }
//
//                    $expertisePayment->expertise_id = $checkNewOrOld->id;
//                    $expertisePayment->case_id = $request->odeme_hesap[$key];
//                    $expertisePayment->amount = (float)$temizVeri;
//                    $expertisePayment->type = $request->odeme_tip[$key];
//                    $expertisePayment->save();
//
//                    logRecord(
//                        "add_expertise_payment_for_expertise",
//                        $authUser->name . " adlı kullanıcı ".$checkNewOrOld->getCari?->unvan." adlı müşterinin ekspertiz kaydına ".(float)str_replace(',','.',$request->odeme_tutar[$key]). "₺ ödeme ekledi.",
//                        $expertisePayment->id
//                    );
//                }
//            }
        }else{
            $logText = $authUser->name . " adlı kullanıcı ".$checkNewOrOld->getCari?->unvan." adlı müşterinin ekspertiz kaydının";
            if (isset($request->audio_url) && $checkNewOrOld->audio_url != $request->audio_url){
                $logText .= " Ses kayıt yolunu $checkNewOrOld->audio_url => $request->audio_url olarak güncelledi.";
                $checkNewOrOld->audio_url = $request->audio_url;
            }

            $canSeeDetailed = in_array($authUser->user_role_group_id,__('arrays.expertiseDetailSeeUsers'));
            if ($canSeeDetailed){
                if (isset($request->payment_type) && $checkNewOrOld->payment_type != $request->payment_type){
                    $logText .= " Ödeme tipini $checkNewOrOld->payment_type => $request->payment_type olarak güncelledi.";
                    $checkNewOrOld->payment_type = $request->payment_type;
                }

                if ($request->stock_id){
                    foreach ($request->stock_id as $key => $item){
                        if ($item > 0){
                            $expertiseStock = ExpertiseStock::where('expertise_id',$checkNewOrOld->id)
//                            ->where('stock_id',$request->stock_id[$key])
                                ->first();

                            if(empty($expertiseStock)){
                                $expertiseStock = new ExpertiseStock();
                                $newStock = Stock::where('id',$request->stock_id[$key])->first();
                                $logText .= $newStock->ad." adlı hizmet ekledi.";
                            }elseif($expertiseStock->stock_id != $request->stock_id[$key]){
                                $logText .= "hizmetinin id'sini $expertiseStock->stock_id => " . (is_array($request->stock_id[$key]) ? implode(',', $request->stock_id[$key]) : $request->stock_id[$key]) . " olarak güncelledi.";                            }
                            $expertiseStock->expertise_id = $checkNewOrOld->id;
                            $expertiseStock->stock_id = $request->stock_id[$key];
                            $expertiseStock->campaign_id = $request->campaign_id[$key];
                            $expertiseStock->sorgu_hizmeti = $request->sorgu_hizmeti[$key] == 'Var' ? 1 : 0;
                            $expertiseStock->yol_yardimi = $request->yol_yardimi[$key] == 'Var' ? 1 : 0;
                            $expertiseStock->hizmet_tutari = (float)str_replace(',','.',$request->hizmet_tutari[$key]) - (float)str_replace(',','.',$request->iskonto_amount[$key]);
                            $expertiseStock->liste_fiyati = (float)str_replace(',','.',$request->liste_fiyati[$key]);
                            $expertiseStock->iskonto_amount = (float)str_replace(',','.',$request->iskonto_amount[$key]);
                            $expertiseStock->save();
                        }
                    }
                }
                if ($request->payment_type == 'plus_kart'){
                    if ((int)$request->plus_kart_id < 1){
                        return redirect()->route('expertises.create')->with('error','Plus Card Bulunamadı!');
                    }

                    $plusCard = PlusCard::find($request->plus_kart_id);
                    $balance = $plusCard->getTotalBalanceStock($request->stock_id[0],$expertise->branch_id);
                    if($request->plus_card_payment_type == "puan" && $balance['points'] > 0){
                        $stock_id = $request->stock_id[0];


                        $last_puan = PlusCardCrediAndPuanAdd::where('card_id',$plusCard->id)
                            ->where('puan_branche_id',$expertise->branch_id)
                            ->orderBy('id','asc')
                            ->get()->map(function($item) use ($stock_id) {
                                if ($item->valid_date == null || $item->valid_date >= date('Y-m-d')) {
                                    if($item->getStockPivot->where('stock_id',$stock_id)->count() > 0){
                                        return $item;
                                    }
                                }
                            });


                        if(!empty($last_puan)){
                            $odeme_id = 0;
                            $unit_price = 0;
                            foreach($last_puan as $lp){
                                if(!empty($lp->id)){
                                    $lps = ExpertisePayment::where('type','plus_kart')
                                        ->where('plus_card_id',$plusCard->id)
                                        ->where('plus_card_odeme_id',$lp->id)
                                        ->count();
                                    $puanAdet = $lps;
                                    $removeCount = PlusCardCrediAndPuanRemove::where('plus_card_credi_and_puan_add_id',$lp->id)->sum('amount');
                                    $puanAdet += $removeCount;
                                    $checkOldPayment = ExpertisePayment::where('expertise_id',$expertise->id)->where('plus_card_odeme_id',$lp->id)->first();
                                    if ($checkOldPayment)
                                        $puanAdet += 1;
                                    if($puanAdet < $lp->puan){
                                        $odeme_id = $lp->id;
                                        $unit_price = $lp->unit_price;
                                        break;
                                    }
                                }
                            }
                            if($odeme_id != 0){
                                $oldPayments = ExpertisePayment::where('expertise_id',$expertise->id)->get();
                                foreach ($oldPayments as $oldPayment){
                                    $oldPayment->delete();
                                    logRecord("delete_expertise_payment",
                                        $authUser->name . " adlı kullanıcı ".$expertise->id." id'li ekspertiz kaydının ".$oldPayment->id." id'li ödeme kaydını kaydını sildi.",
                                        $oldPayment->id
                                    );
                                }


                                $expertisePayment = new ExpertisePayment();
                                $expertisePayment->expertise_id = $expertise->id;
                                $expertisePayment->case_id = 0;
                                $expertisePayment->amount = $unit_price ?? 0;
                                $expertisePayment->type = 'plus_kart';
                                $expertisePayment->plus_card_id = $plusCard->id;
                                $expertisePayment->plus_card_odeme_id = $odeme_id;
                                $expertisePayment->save();
                            }else{
                                return redirect()->route('expertises.create')->with('error','Plus Card Puan Bakiye Yetersiz!');
                            }

                        }else{
                            return redirect()->route('expertises.create')->with('error','Plus Card Puan Yükleme Bulunamadı!');
                        }
                    }
                    if($request->plus_card_payment_type == "kredi" && $balance['credits'] > 0){
                        $stock_id = $request->stock_id[0];
                        $last_credits = PlusCardCrediAndPuanAdd::
//                    search($plusCard->id)
                        where('card_id',$plusCard->id)
                            ->orderBy('id','asc')
                            ->get()->map(function($item) use ($stock_id) {
                                if ($item->valid_date == null || $item->valid_date >= date('Y-m-d')) {
                                    if($item->getStockPivot->where('stock_id',$stock_id)->count() > 0){
                                        return $item;
                                    }
                                }elseif ($item->definitions_id > 0){
                                    $definition = PlusCardsDefinitions::where('id',$item->definitions_id)->first();
                                    if ($definition && $definition->unit_price == $item->unit_price){
                                        if($item->getStockPivot->where('stock_id',$stock_id)->count() > 0){
                                            return $item;
                                        }
                                    }
                                }
                            });
                        if(!empty($last_credits)){
                            $odeme_id = 0;
                            $unit_price = 0;
                            foreach($last_credits as $lp){
                                if(!empty($lp->id)){
                                    $lps = ExpertisePayment::where('type','plus_kart')
                                        ->where('plus_card_id',$plusCard->id)
                                        ->where('plus_card_odeme_id',$lp->id)
                                        ->count();
                                    $krediAdet = $lps;
                                    $removeCount = PlusCardCrediAndPuanRemove::where('plus_card_credi_and_puan_add_id',$lp->id)->sum('amount');
                                    $krediAdet += $removeCount;
                                    $checkOldPayment = ExpertisePayment::where('expertise_id',$expertise->id)->where('plus_card_odeme_id',$lp->id)->first();
                                    if ($checkOldPayment)
                                        $krediAdet += 1;
                                    if($krediAdet < $lp->credi){
                                        $odeme_id = $lp->id;
                                        $unit_price = $lp->unit_price;
                                        break;
                                    }
                                }

                            }

                            if($odeme_id != 0){
                                $oldPayments = ExpertisePayment::where('expertise_id',$expertise->id)->get();
                                foreach ($oldPayments as $oldPayment){
                                    $oldPayment->delete();
                                    logRecord("delete_expertise_payment",
                                        $authUser->name . " adlı kullanıcı ".$expertise->id." id'li ekspertiz kaydının ".$oldPayment->id." id'li ödeme kaydını kaydını sildi.",
                                        $oldPayment->id
                                    );
                                }
                                $expertisePayment = new ExpertisePayment();
                                $expertisePayment->expertise_id = $expertise->id;
                                $expertisePayment->case_id = 0;
                                $expertisePayment->amount = $unit_price ?? 0;
                                $expertisePayment->type = 'plus_kart';
                                $expertisePayment->plus_card_id = $plusCard->id;
                                $expertisePayment->plus_card_odeme_id = $odeme_id;
                                $expertisePayment->save();
                            }else{
                                return redirect()->route('expertises.create')->with('error','Plus Card Kredi Yüklemesi Yetersiz!');
                            }

                        }else{
                            return redirect()->route('expertises.create')->with('error','Plus Card Kredi Yüklemesi Bulunamadı!');
                        }
                    }

                }
                elseif ($request->payment_type == 'sozlesme'){
                    $check = ContractController::contractChecker($request->contract_no,$request->contract_code,$request->branch_id,$request->payment_id,$request->car_id);

                    if ($check['success'] === false)
                        return redirect()->route('expertises.create')->with('error',$check['message']);

                    $contract = $check['contract'];
                    $contractCode = $check['contractCode'];

                    ExpertisePayment::where('expertise_id',$expertise->id)->delete();
                    foreach ($request->stock_id as $stockID){
                        $contractStock = ContractStock::where(['contract_id'=>$contract->id,'stock_id'=>$stockID,'type'=>$contractCode->type])->first();
                        if ($contractStock){
                            $expertisePayment = new ExpertisePayment();
                            $expertisePayment->expertise_id = $expertise->id;
                            $expertisePayment->case_id = 0;
                            $expertisePayment->amount = $contractStock->price;
                            $expertisePayment->type = 'sozlesme';
                            $expertisePayment->payment_code = $request->contract_no;
                            $expertisePayment->payment_detail = $request->contract_code;
                            $expertisePayment->save();
                        }
                    }

                    $contractCode->invoice_customer_id = $contractCode->type == 'alici' ? ((int)$request->alici_id ?? (int)$request->cari_id) : ($contractCode->type == 'satici' ? ((int)$request->satici_id ?? (int)$request->cari_id) : $contract->customer_id);
                    $contractCode->used = 1;
                    $contractCode->save();
                }
                else{
                    if ($request->odeme_tip){
                        $odemeAdet = 0;
                        $ilkKayit = false;
                        foreach ($request->odeme_tip as $key => $tip){
                            $temizVeri = str_replace('₺', '', $request->odeme_tutar[$key]);
                            $temizVeri = str_replace('.', '', $temizVeri);
                            $temizVeri = str_replace(',','.', $temizVeri);



//                        $expertisePayment = ExpertisePayment::where('expertise_id',$expertise->id)
//                            ->where('case_id',$request->odeme_hesap[$key])
//                            ->first();
//                        if(empty($expertisePayment)){
//                            $expertisePayment = new ExpertisePayment();
//                        }
                            $expertisePayment = new ExpertisePayment();
                            $expertisePayment->expertise_id = $expertise->id;
                            $expertisePayment->case_id = $request->odeme_hesap[$key];
                            $expertisePayment->amount = (float)$temizVeri;
                            $expertisePayment->type = $request->odeme_tip[$key];
                            $expertisePayment->save();
                            if($expertisePayment && $ilkKayit == false)
                                $ilkKayit = $expertisePayment;

                            $odemeAdet++;
                            logRecord(
                                "add_expertise_payment_for_expertise",
                                $authUser->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydına ".(float)str_replace(',','.',$request->odeme_tutar[$key]). "₺ ödeme ekledi.",
                                $expertisePayment->id
                            );
                        }

                        if ($ilkKayit){
                            $oldPayments = ExpertisePayment::where('expertise_id',$expertise->id)->where('created_at','<',$ilkKayit->created_at)->get();
                            foreach ($oldPayments as $oldPayment){
                                $oldPayment->delete();
                                logRecord("delete_expertise_payment",
                                    $authUser->name . " adlı kullanıcı ".$expertise->id." id'li ekspertiz kaydının ".$oldPayment->id." id'li ödeme kaydını kaydını sildi.",
                                    $oldPayment->id
                                );
                            }

                        }

                        if ($odemeAdet == 0){
                            return back()->with('error','Ödeme Seçilmedi!');
                        }
                    }else{
                        return back()->with('error','Ödeme Seçilmedi!');
                    }
                }
            }

            $checkNewOrOld->save();
            logRecord(
                "edit_expertise",
                $logText,
                $checkNewOrOld->id
            );
        }

        if ($request->plus_card_sms_verified == 1){
            $expertisePlusCardSmsVerification = new ExpertisePlusCardSmsVerification();
            $expertisePlusCardSmsVerification->expertise_id = $expertise->id;
            $expertisePlusCardSmsVerification->cep = $request->plus_card_sms_cep;
            $expertisePlusCardSmsVerification->telefon = $request->plus_card_sms_telefon;
            $expertisePlusCardSmsVerification->code_telefon = $request->plus_card_sms_telefon_code;
            $expertisePlusCardSmsVerification->code_cep = $request->plus_card_sms_cep_code;
            $expertisePlusCardSmsVerification->type = $request->plus_card_sms_verified_type;
            $expertisePlusCardSmsVerification->save();
        }

        if (isset($request->return_url))
            return redirect($request->return_url)->with('success','Başarılı');

        return redirect()->route('expertises.edit',$expertise->uuid)->with('success','Başarılı');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Expertise $expertise)
    {
        $authUser = \auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['delete_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $expertise->delete();
        ExpertisePayment::where('expertise_id',$expertise->id)->delete();

        logRecord(
            "delete_expertise",
            $authUser->name." ".$expertise->uuid." id'li ekspertiz kaydını sildi.",
            $expertise->id
        );

        return  redirect()->route('expertises.index')->with('success','Kayıt Silindi');
    }

    public function details(
        ExpertiseBodyworkService            $expertiseBodyworkService,
        ExpertiseInternalControlService     $expertiseInternalControlService,
        ExpertiseTireAndRimService          $expertiseTireAndRimService,
        ExpertiseSubControlAndEngineService $expertiseSubControlAndEngineService,
        ExpertiseComponentService           $expertiseComponentService
    ) {
        $authUser = \auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['list_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        if (!isset($_GET['type']) || $_GET['type'] == '')
            return redirect()->route('expertises.index')->with('error','Kayıt Bulunamadı');
        if (!isset($_GET['expertise_id']) || $_GET['expertise_id'] == '')
            return redirect()->route('expertises.index')->with('error','Kayıt Bulunamadı');

        if (!in_array($authUser->user_role_group_id,[30,31,32,33,34,35,37,39,40,42,44,45,47]) && $_GET['type'] == 'car')
            return redirect()->route('index')->with('error','Yetkiniz Yok!');
        elseif (!in_array($authUser->user_role_group_id,[30,33,39,40,44,45]) && $_GET['type'] == 'bodywork')
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $pageTitle = '';
        $unlock_tabs = array();

        $newDB = Expertise::where('uuid',$_GET['expertise_id'])->first();
        if ($newDB){
            $car = Car::where('id',$newDB->car_id)->select('id','plaka','sase_no','km','car_group_tip_id','car_group_marka_id','car_group_model_id','car_case_type_id','car_fuels_id','car_gears_id','motor_hacmi','model_yili','cekis')->first();
            $stocks = [];
            foreach ($newDB->getStocks as $getStock){
                $stocks[] = ['ad'=>$getStock->getStock?->ad];
            }
            $islemler = [
                'arac' => $newDB->arac_kontrol,
                'fren' => $newDB->fren_kontrol,
                'kaporta' => $newDB->kaporta_kontrol,
                'diagnostic' => $newDB->diagnostic_kontrol,
                'lastik' => $newDB->lastik_jant_kontrol,
                'motor' => $newDB->alt_motor_kontrol,
                'ic' => $newDB->ic_kontrol,
                'komponent' => $newDB->komponent_kontrol,
                'sorgulama' => $newDB->sorgu_hizmeti,
                'co2'=>$newDB->co2_kontrol,
            ];


            foreach($newDB->getStocks as $gt){
                if($gt->getStock->car == 1 && !in_array('car', $unlock_tabs)){
                    $unlock_tabs[] = 'car';
                }
                if($gt->getStock->brake == 1 && !in_array('brake', $unlock_tabs)){
                    $unlock_tabs[] = 'brake';
                }
                if($gt->getStock->bodywork == 1 && !in_array('bodywork', $unlock_tabs)){
                    $unlock_tabs[] = 'bodywork';
                }
                if($gt->getStock->internal_control == 1 && !in_array('internal_control', $unlock_tabs)){
                    $unlock_tabs[] = 'internal_control';
                }
                if($gt->getStock->tire_and_rim == 1 && !in_array('tire_and_rim', $unlock_tabs)){
                    $unlock_tabs[] = 'tire_and_rim';
                }
                if($gt->getStock->sub_control_and_engine == 1 && !in_array('sub_control_and_engine', $unlock_tabs)){
                    $unlock_tabs[] = 'sub_control_and_engine';
                }
                if($gt->getStock->component == 1 && !in_array('component', $unlock_tabs)){
                    $unlock_tabs[] = 'component';
                }
                if($gt->getStock->diagnostic == 1 && !in_array('diagnostic', $unlock_tabs)){
                    $unlock_tabs[] = 'diagnostic';
                }

                if(!empty($gt->getStock) && ($gt->getStock->co2 == 1 || $gt->getStock->id == 209 || $gt->campaign_id == 261 || $gt->campaign_id == 267)){
                    $unlock_tabs[] = 'co2';
                }
                if($gt->getStock->sorgu_hizmeti == 1){
                    $unlock_tabs[] = "query";
                }
            }

            if ($_GET['type'] == 'car') {
                $expertise = [
                    'uuid' => $newDB->uuid,
                    'car_id' => $newDB->car_id,
                    'arac_kontrol_user' => $newDB->arac_kontrol_user,
                    'plaka' => $car->plaka ?? '',
                    'sase_no' => $newDB->sase_no ? $newDB->sase_no : ($car->sase_no ?? ''),
                    'km' => $newDB->km,
                    'km_type' => $newDB->km_type,
                    'car_group' => $car->getGroup?->name,
                    'car_marka' => $car->getMarka?->name,
                    'car_model' => $car->getModel?->name,
                    'car_case_type' => $car->getCaseType?->name,
                    'car_fuel' => $car->getFuel?->name,
                    'car_gear' => $car->getGear?->name,
                    'motor_hacmi' => $car->motor_hacmi,
                    'cekis' => $car->cekis,
                    'model_yili' => $car->model_yili,
                    'is_closed' => $authUser->type == 'admin' ? 0 : ($newDB->ftp_ok == 1 ? 1 : 0),
                    'car_case_type_id' => $car->car_case_type_id,
                    'car_fuels_id' => $car->car_fuels_id,
                    'car_gears_id' => $car->car_gears_id,
                    'car_group_model_id' => $car->car_group_model_id,
                ];
            } elseif ($_GET['type'] == 'brake') {
                if ($authUser->user_role_group_id == 33) {
                    return redirect()->back()->with('error', 'Yetkiniz Yok!');
                }
                $editable = 1;
                $isClosed = 0;
                if ($authUser->type != 'admin' && $newDB->ftp_ok == 1) {
                    $isClosed = 1;
                    $editable = 0;
                }

                if ($isClosed == 0 && $newDB->getBranch?->brake_values_auto == 1) {
                    $editable = 0;
                }

                $getBrake = ExpertiseBrake::where('expertise_id', $newDB->id)->first();
                $getAllNotes = Note::where('key', 'brake')->get();
                $expertise = [
                    'uuid' => $newDB->uuid,
                    'belge_no' => $newDB->belge_no,
                    'fren_kontrol_user' => $newDB->fren_kontrol_user,
                    'plaka' => $car->plaka,
                    'sase_no' => $newDB->sase_no ? $newDB->sase_no : $car->sase_no,
                    'date' => \Carbon\Carbon::make($newDB->belge_tarihi)->format('Y-m-d H:i:s'),
                    'car_model' => $car->getModel?->name,
                    'car_model_year' => $car->model_yili,
                    'getStocks' => $stocks,
                    'km' => $newDB->km,
                    'yanal_kayma_on' => $getBrake?->yanal_kayma_on ?? '',
                    'yanal_kayma_arka' => $getBrake?->yanal_kayma_arka ?? '',
                    'max_kuvvet_on_1' => $getBrake?->max_kuvvet_on_1 ?? '',
                    'max_kuvvet_on_2' => $getBrake?->max_kuvvet_on_2 ?? '',
                    'max_kuvvet_arka_1' => $getBrake?->max_kuvvet_arka_1 ?? '',
                    'max_kuvvet_arka_2' => $getBrake?->max_kuvvet_arka_2 ?? '',
                    'dengesizlik_orani_on' => $getBrake?->dengesizlik_orani_on ?? '',
                    'dengesizlik_orani_arka' => $getBrake?->dengesizlik_orani_arka ?? '',
                    'yalpa_orani_on_1' => $getBrake?->yalpa_orani_on_1 ?? '',
                    'yalpa_orani_on_2' => $getBrake?->yalpa_orani_on_2 ?? '',
                    'yalpa_orani_arka_1' => $getBrake?->yalpa_orani_arka_1 ?? '',
                    'yalpa_orani_arka_2' => $getBrake?->yalpa_orani_arka_2 ?? '',
                    'suspansiyon_on_1' => $getBrake?->suspansiyon_on_1 ?? '',
                    'suspansiyon_on_2' => $getBrake?->suspansiyon_on_2 ?? '',
                    'suspansiyon_arka_1' => $getBrake?->suspansiyon_arka_1 ?? '',
                    'suspansiyon_arka_2' => $getBrake?->suspansiyon_arka_2 ?? '',
                    'el_freni_dengesizlik_orani' => $getBrake?->el_freni_dengesizlik_orani ?? '',
                    'el_freni_kuvvet_a' => $getBrake?->el_freni_kuvvet_a ?? '',
                    'el_freni_kuvvet_b' => $getBrake?->el_freni_kuvvet_b ?? '',
                    'on_dingil_bosta_a' => $getBrake?->on_dingil_bosta_a ?? '',
                    'on_dingil_bosta_b' => $getBrake?->on_dingil_bosta_b ?? '',
                    'arka_dingil_bosta_a' => $getBrake?->arka_dingil_bosta_a ?? '',
                    'arka_dingil_bosta_b' => $getBrake?->arka_dingil_bosta_b ?? '',
                    'allNotes' => $getAllNotes,
                    'getNotes' => $getBrake?->getNotes ?? [],
                    'is_closed' => $isClosed,
                    'editable' => $editable
                ];
            } elseif ($_GET['type'] == 'bodywork') {
                if ($authUser->user_role_group_id == 34 || $authUser->user_role_group_id == 45) {
                    return redirect()->back()->with('error', 'Yetkiniz Yok!');
                }

                $getAllNotes = Note::where('key', 'bodywork')->get();
                $coordinates = BodyworkCoordinate::where('expertise_id', $newDB->id)->first() ?? [];

                if ($newDB->created_at < '2024-05-28 00:00:00')
                    $bodyworkImage = 'https://umram.online/storage/' . ($newDB->bodywork_image ? $newDB->bodywork_image : ($newDB->getCar && $newDB->getCar->getCaseType ? $newDB->getCar->getCaseType->image : \App\Models\CarCaseType::where('status', 1)->first()->image));
                elseif ($coordinates != [])
                    $bodyworkImage = '/storage/car_case_type/' . $coordinates->image_url . '.jpg';
                else
                    $bodyworkImage = '/storage/' . ($newDB->getCar && $newDB->getCar->getCaseType ? $newDB->getCar->getCaseType->image : \App\Models\CarCaseType::where('status', 1)->first()->image);

                $expertise = [
                    'is_closed' => $authUser->type == 'admin' ? 0 : ($newDB->ftp_ok == 1 ? 1 : 0),
                    'uuid' => $newDB->uuid,
                    'kaporta_kontrol_user' => $newDB->kaporta_kontrol_user,
                    'plaka' => $car->plaka,
                    'sase_no' => $newDB->sase_no ? $newDB->sase_no : $car->sase_no,
                    'belge_no' => $newDB->belge_no,
                    'date' => \Carbon\Carbon::make($newDB->belge_tarihi)->format('Y-m-d H:i:s'),
                    'car_model' => $car->getModel?->name,
                    'car_model_year' => $car->model_yili,
                    'case_image' => $car->getCaseType?->image,
                    'getStocks' => $stocks,
                    'km' => $newDB->km,
                    'getBodyworks' => $expertiseBodyworkService->getExpertControls($newDB),
                    'getBodyworkNotes' => $newDB->getBodyworkNotes,
                    'allNotes' => $getAllNotes,
                    'created_at' => $newDB->created_at,
                    'bodywork_image' => $bodyworkImage,
                    'coordinates' => $coordinates
                ];
            } elseif ($_GET['type'] == 'diagnostic') {
                if ($authUser->user_role_group_id == 33 || $authUser->user_role_group_id == 44 || $authUser->user_role_group_id == 47) {
                    return redirect()->back()->with('error', 'Yetkiniz Yok!');
                }
                $getAllNotes = Note::where('key', 'diagnostic')->get();

                $expertise = [
                    'is_closed' => $authUser->type == 'admin' ? 0 : ($newDB->ftp_ok == 1 ? 1 : 0),
                    'uuid' => $newDB->uuid,
                    'diagnostic_file' => '/storage/' . $newDB->diagnostic_file,
                    'diagnostic_kontrol_user' => $newDB->diagnostic_kontrol_user,
                    'plaka' => $car->plaka,
                    'sase_no' => $newDB->sase_no ? $newDB->sase_no : $car->sase_no,
                    'belge_no' => $newDB->belge_no,
                    'date' => \Carbon\Carbon::make($newDB->belge_tarihi)->format('Y-m-d H:i:s'),
                    'car_model' => $car->getModel?->name,
                    'car_model_year' => $car->model_yili,
                    'getStocks' => $stocks,
                    'km' => $newDB->km,
                    'getDiagnosticNotes' => $newDB->getDiagnosticNotes,
                    'allNotes' => $getAllNotes,
                    'getDiagnostics' => $newDB->getDiagnostics
                ];
            } elseif ($_GET['type'] == 'internal_control') {
                if ($authUser->user_role_group_id == 33 || $authUser->user_role_group_id == 47) {
                    return redirect()->back()->with('error', 'Yetkiniz Yok!');
                }

                $getAllNotes = Note::where('key', 'internal')->get();

                $expertise = [
                    'is_closed' => $authUser->type == 'admin' ? 0 : ($newDB->ftp_ok == 1 ? 1 : 0),
                    'uuid' => $newDB->uuid,
                    'ic_kontrol_user' => $newDB->ic_kontrol_user,
                    'plaka' => $car->plaka,
                    'sase_no' => $newDB->sase_no ? $newDB->sase_no : $car->sase_no,
                    'belge_no' => $newDB->belge_no,
                    'date' => \Carbon\Carbon::make($newDB->belge_tarihi)->format('Y-m-d H:i:s'),
                    'car_model' => $car->getModel?->name,
                    'car_model_year' => $car->model_yili,
                    'getStocks' => $stocks,
                    'km' => $newDB->km,
                    'getInternalControlsNotes' => $newDB->getInternalControlsNotes,
                    'allNotes' => $getAllNotes,
                    'getInternalControls' => $expertiseInternalControlService->getExpertControls($newDB),
                ];
            } elseif ($_GET['type'] == 'tire_and_rim') {
                if ($authUser->user_role_group_id == 33 || $authUser->user_role_group_id == 47) {
                    return redirect()->back()->with('error', 'Yetkiniz Yok!');
                }

                $getAllNotes = Note::where('key', 'tire_and_rim')->get();

                $expertise = [
                    'is_closed' => $authUser->type == 'admin' ? 0 : ($newDB->ftp_ok == 1 ? 1 : 0),
                    'uuid' => $newDB->uuid,
                    'lastik_kontrol_user' => $newDB->lastik_kontrol_user,
                    'plaka' => $car->plaka,
                    'sase_no' => $newDB->sase_no ? $newDB->sase_no : $car->sase_no,
                    'belge_no' => $newDB->belge_no,
                    'date' => \Carbon\Carbon::make($newDB->belge_tarihi)->format('Y-m-d H:i:s'),
                    'car_model' => $car->getModel?->name,
                    'car_model_year' => $car->model_yili,
                    'getStocks' => $stocks,
                    'km' => $newDB->km,
                    'getTireAndRimNotes' => $newDB->getTireAndRimNotes,
                    'allNotes' => $getAllNotes,
                    'getTireAndRims' => $expertiseTireAndRimService->getExpertControls($newDB),
                ];
            } elseif ($_GET['type'] == 'sub_controls_and_engine') {
                if ($authUser->user_role_group_id == 33 || $authUser->user_role_group_id == 47) {
                    return redirect()->back()->with('error', 'Yetkiniz Yok!');
                }

                $pageTitle = $expertiseSubControlAndEngineService->getPageTitle($car);

                $getAllNotes = Note::where('key', 'sub_control')->get();

                $expertise = [
                    'is_closed' => $authUser->type == 'admin' ? 0 : ($newDB->ftp_ok == 1 ? 1 : 0),
                    'uuid' => $newDB->uuid,
                    'alt_motor_kontrol_user' => $newDB->alt_motor_kontrol_user,
                    'plaka' => $car->plaka,
                    'sase_no' => $newDB->sase_no ? $newDB->sase_no : $car->sase_no,
                    'belge_no' => $newDB->belge_no,
                    'date' => \Carbon\Carbon::make($newDB->belge_tarihi)->format('Y-m-d H:i:s'),
                    'car_model' => $car->getModel?->name,
                    'car_model_year' => $car->model_yili,
                    'getStocks' => $stocks,
                    'km' => $newDB->km,
                    'allNotes' => $getAllNotes,
                    'getSubControlsAndEngines' => $expertiseSubControlAndEngineService->getExpertControls($newDB),
                    'getSubControlsAndEngineNotes' => $newDB->getSubControlsAndEngineNotes,
                    'co2_test' => (!empty($newDB->getStockhasOne) && ($newDB->getStockhasOne?->getStock?->co2 == 1 || $newDB->getStockhasOne->campaign_id == 261)) ? true : false,
                ];
            } elseif ($_GET['type'] == 'component') {
                if ($authUser->user_role_group_id == 47) {
                    return redirect()->back()->with('error', 'Yetkiniz Yok!');
                }

                $getAllNotes = Note::where('key', 'sub_control')->get();

                $expertise = [
                    'is_closed' => $authUser->type == 'admin' ? 0 : ($newDB->ftp_ok == 1 ? 1 : 0),
                    'uuid' => $newDB->uuid,
                    'komponent_kontrol_user' => $newDB->komponent_kontrol_user,
                    'plaka' => $car->plaka,
                    'sase_no' => $newDB->sase_no ? $newDB->sase_no : $car->sase_no,
                    'belge_no' => $newDB->belge_no,
                    'date' => \Carbon\Carbon::make($newDB->belge_tarihi)->format('Y-m-d H:i:s'),
                    'car_model' => $car->getModel?->name,
                    'car_model_year' => $car->model_yili,
                    'getStocks' => $stocks,
                    'km' => $newDB->km,
                    'allNotes' => $getAllNotes,
                    'getComponents' => $expertiseComponentService->getExpertControls($newDB),
                    'getComponentNotes' => $newDB->getComponentNotes,
                    'dyno_data' => $newDB->getDyno,
                ];
            } elseif ($_GET['type'] == 'co2') {
                $expertise_co2 = ExpertiseCo2::where('expertise_id', $newDB->id)->first();
                $getAllNotes = Note::where('key', 'co2')->get();

                $expertise = [
                    'is_closed' => 0,
                    'uuid' => $newDB->uuid,
                    'co2_kontrol_user' => $newDB->komponent_kontrol_user,
                    'plaka' => $car->plaka,
                    'sase_no' => $newDB->sase_no ? $newDB->sase_no : $car->sase_no,
                    'belge_no' => $newDB->belge_no,
                    'date' => \Carbon\Carbon::make($newDB->belge_tarihi)->format('Y-m-d H:i:s'),
                    'car_model' => $car->getModel?->name,
                    'getStocks' => $stocks,
                    'km' => $newDB->km,
                    'allNotes' => $getAllNotes,
                    'getCo2Note' => $newDB->getCo2Notes,
                    'expertise_co2' => $expertise_co2,
                ];
            } elseif ($_GET['type'] == 'query') {
                $getComponents = [];

// Retrieve all component records for the given expertise_id in one query
                $components = ExpertiseComponent::where('expertise_id', $newDB->id)->get()->keyBy('key');

                foreach (__('arrays.components') as $key => $tireAndRim) {
                    $getInternalcontrol = $components->get($key);
                    $getComponents[$key] = [
                        'status' => $getInternalcontrol?->status ?? 0,
                        'answer' => $getInternalcontrol?->answer ?? '',
                        'note' => $getInternalcontrol?->note ?? '',
                        'title' => $tireAndRim
                    ];
                }

                $getQueryLog = QueryLog::where("uuid",$newDB->uuid)->first();
                $getAllNotes = Note::where('key','sub_control')->get();
                $customer = Customer::where("id",$newDB->satici_id)->first();
                $kimlik = null;
                if($customer?->type == "bireysel"){
                    $kimlik = $customer?->tc_no;
                }elseif($customer?->type == "kurumsal"){
                    $kimlik = $customer?->vergi_no;
                }
                $settings = Setting::first();

                $hasarPlakaSorguUrl = $settings->hasar_sorgu_firmasi == 'otosorgu' ? 'otosorgu' : 'arabasorgula';
                $hasarSaseSorguUrl = $settings->hasar_sorgu_firmasi == 'otosorgu' ? 'otosorgu_sasi' : 'arabasorgula-sasi';
                $kilometrePlakaSorguUrl = $settings->kilometre_sorgu_firmasi == 'otosorgu' ? 'otosorgu' : 'arabasorgula';
                $kilometreSaseSorguUrl = $settings->kilometre_sorgu_firmasi == 'otosorgu' ? 'otosorgu_sasi' : 'arabasorgula-sasi';
                $borcPlakaSorguUrl = $settings->borc_sorgu_firmasi == 'otosorgu' ? 'otosorgu' : 'arabasorgula';
                $borcSaseSorguUrl = $settings->borc_sorgu_firmasi == 'otosorgu' ? 'otosorgu_sasi' : 'arabasorgula-sasi';
                $detayPlakaSorguUrl = $settings->arac_detay_sorgu_firmasi == 'otosorgu' ? 'otosorgu' : 'arabasorgula';
                $detaySaseSorguUrl = $settings->arac_detay_sorgu_firmasi == 'otosorgu' ? 'otosorgu_sasi' : 'arabasorgula-sasi';
                $degisenPlakaSorguUrl = $settings->degisen_sorgu_firmasi == 'otosorgu' ? 'otosorgu' : 'arabasorgula';
                $degisenSaseSorguUrl = $settings->degisen_sorgu_firmasi == 'otosorgu' ? 'otosorgu_sasi' : 'arabasorgula-sasi';
                $ruhsatPlakaSorguUrl = $settings->ruhsat_sorgu_firmasi == 'otosorgu' ? 'otosorgu' : 'arabasorgula';
                $ruhsatSaseSorguUrl = $settings->ruhsat_sorgu_firmasi == 'otosorgu' ? 'otosorgu_sasi' : 'arabasorgula-sasi';

                $expertise = [
                    'is_closed' => $authUser->type == 'admin' ? 0 : ($newDB->ftp_ok == 1 ? 1 : 0),
                    'uuid' => $newDB->uuid,
                    'komponent_kontrol_user' => $newDB->komponent_kontrol_user,
                    'plaka' => $car->plaka,
                    'sase_no' => $newDB->sase_no ? $newDB->sase_no : $car->sase_no,
                    'kimlik' => $kimlik,
                    'belge_no' => $newDB->belge_no,
                    'date' =>\Carbon\Carbon::make($newDB->belge_tarihi)->format('Y-m-d H:i:s'),
                    'car_model' => $car->getModel?->name,
                    'getStocks' =>$stocks,
                    'km' => $newDB->km,
                    'allNotes' => $getAllNotes,
                    'getComponents' => $getComponents,
                    'getComponentNotes' => $newDB->getComponentNotes,
                    'dyno_data' => $newDB->getDyno,
                    'queryLogs' => $getQueryLog,
                    'hasarPlakaSorguUrl' => $hasarPlakaSorguUrl,
                    'hasarSaseSorguUrl' => $hasarSaseSorguUrl,
                    'kilometrePlakaSorguUrl' => $kilometrePlakaSorguUrl,
                    'kilometreSaseSorguUrl' => $kilometreSaseSorguUrl,
                    'borcPlakaSorguUrl' => $borcPlakaSorguUrl,
                    'borcSaseSorguUrl' => $borcSaseSorguUrl,
                    'detayPlakaSorguUrl' => $detayPlakaSorguUrl,
                    'detaySaseSorguUrl' => $detaySaseSorguUrl,
                    'degisenPlakaSorguUrl' => $degisenPlakaSorguUrl,
                    'degisenSaseSorguUrl' => $degisenSaseSorguUrl,
                    'ruhsatPlakaSorguUrl' => $ruhsatPlakaSorguUrl,
                    'ruhsatSaseSorguUrl' => $ruhsatSaseSorguUrl,
                ];
            }

        }
        elseif (!env('APP_LOCAL')){
            $oldDB = DB::connection('mysql2')->table('T400_SRVBASLIK')->where('T400_UQ',$_GET['expertise_id'])->first();
            if (!$oldDB)
                return redirect()->route('expertises.index')->with('error','Kayıt Bulunamadı!');

            $islemler = [
                'arac' => 1,
                'fren' => 1,
                'kaporta' => 1,
                'diagnostic' => 1,
                'lastik' => 1,
                'motor' => 1,
                'ic' => 1,
                'komponent' => 1,
                'co2' => 1,
            ];

            $car = DB::connection('mysql2')->table('T202_ARACLAR')->where('T202_UQ',$oldDB->T400_arac_UQ)->first();
            $model = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')->where('T107_kod',$car->T202_aracgrupkodu ?? 0)->first();
            if ($model)
                $marka = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')->where('T107_kod','LIKE',substr($model->T107_kod,0,5)."%")->where('T107_gruptipi',5)->first();
            else
                $marka = null;
            if ($marka)
                $grup = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')->where('T107_kod','LIKE',substr($marka->T107_kod,0,2)."%")->where('T107_gruptipi',4)->first();
            else
                $grup = null;

            $kasaTipi = DB::connection('mysql2')->table('T110_KASATIPLERI')->where('T110_kod',$car->T202_kasatipikodu)->first();
            $yakitTipi = DB::connection('mysql2')->table('T108_MOTORTIPLERI')->where('T108_kod',$car->T202_motortipikodu)->first();
            $vitesTipi = DB::connection('mysql2')->table('T109_VITESTIPLERI')->where('T109_kod',$car->T202_vitestipikodu)->first();
            if ($car->T202_motor_cekis == 1)
                $cekis = '4x2';
            elseif ($car->T202_motor_cekis == 2)
                $cekis = '4x4';
            else
                $cekis = 'Yok';
            $stok = DB::connection('mysql2')->table('T203_STOKLAR')->where('T203_UQ',$oldDB->T400_hizmet_UQ)->select('T203_stokadi')->first();
            $stocks = [['ad'=>$stok->T203_stokadi]];
            if ($_GET['type'] == 'car'){
                $expertise = [
                    'uuid' => $oldDB->T400_UQ,
                    'car_id' =>$oldDB->T400_arac_UQ,
                    'arac_kontrol_user' => 0,
                    'plaka' => $car->T202_plakano,
                    'sase_no' => $car->T202_saseno,
                    'km' => $oldDB->T400_arackm,
                    'car_group' => $grup->T107_aciklama ?? '',
                    'car_marka' => $marka->T107_aciklama ?? '',
                    'car_model' => $model->T107_aciklama ?? '',
                    'car_case_type' => $kasaTipi?->T110_aciklama,
                    'car_fuel' => $yakitTipi?->T108_aciklama,
                    'car_gear' => $vitesTipi?->T109_aciklama,
                    'motor_hacmi' => $car->T202_motor_hacmi,
                    'cekis' => $cekis,
                    'model_yili' => $car->T202_modelyili,
                    'is_closed' => 1,
                ];
            }
            elseif ($_GET['type'] == 'brake'){
                $getBrake = DB::connection('mysql2')->table('T420_FREN')->where('T420_servis_UQ',$_GET['expertise_id'])->first();
                $yanalKayma = DB::connection('mysql2')->table('T410_YANALKAYMA')->where('T410_servis_UQ',$_GET['expertise_id'])->first();
                $suspansiyon = DB::connection('mysql2')->table('T430_SUSPANSIYON')->where('T430_servis_UQ',$_GET['expertise_id'])->first();
                $userID = DB::connection('mysql2')->table('T401_SRVTESTLERI')->where('T401_servis_UQ',$_GET['expertise_id'])->where('T401_bolum_kodu',2)->first();
                $notlar = DB::connection('mysql2')->table('T402_SRVNOTLARI')->where('T402_servis_UQ',$_GET['expertise_id'])->where('T402_test_kodu',2)->select('T402_aciklama as note')->get();

                $expertise = [
                    'uuid' => $oldDB->T400_UQ,
                    'belge_no' => $oldDB->T400_belgeno,
                    'fren_kontrol_user' => $userID?->T401_kullanici_UQ,
                    'plaka' => $car->T202_plakano,
                    'sase_no' => $car->T202_saseno,
                    'date' => $getBrake ? \Carbon\Carbon::make($oldDB->T400_belgetarihi)->format('Y-m-d H:i:s') : now()->format('Y-m-d H:i:s'),
                    'car_model' => $model->T107_aciklama ?? '',
                    'getStocks' =>$stocks,
                    'km' => $oldDB->T400_arackm,
                    'yanal_kayma_on' => isset($yanalKayma) ? $yanalKayma?->T410_onkayma : '',
                    'yanal_kayma_arka' => isset($yanalKayma) ? $yanalKayma?->T410_arkakayma : '',
                    'max_kuvvet_on_1' => isset($getBrake) ? $getBrake?->T420_maksimumA : '',
                    'max_kuvvet_on_2' => isset($getBrake) ? $getBrake?->T420_maksimumB : '',
                    'max_kuvvet_arka_1' => isset($getBrake) ? $getBrake?->T420_maksimumC : '',
                    'max_kuvvet_arka_2' => isset($getBrake) ? $getBrake?->T420_maksimumD : '',
                    'dengesizlik_orani_on' => isset($getBrake) ? $getBrake?->T420_onfrendngszOran : '',
                    'dengesizlik_orani_arka' => isset($getBrake) ? $getBrake?->T420_arkafrendngszOran : '',
                    'yalpa_orani_on_1' => isset($getBrake) ? $getBrake?->T420_yalpasurtunmeA : '',
                    'yalpa_orani_on_2' => isset($getBrake) ? $getBrake?->T420_yalpasurtunmeB : '',
                    'yalpa_orani_arka_1' => isset($getBrake) ? $getBrake?->T420_yalpasurtunmeC : '',
                    'yalpa_orani_arka_2' => isset($getBrake) ? $getBrake?->T420_yalpasurtunmeD : '',
                    'suspansiyon_on_1' => isset($suspansiyon) ? $suspansiyon?->T430_OranA : '',
                    'suspansiyon_on_2' => isset($suspansiyon) ? $suspansiyon?->T430_OranB : '',
                    'suspansiyon_arka_1' => isset($suspansiyon) ? $suspansiyon?->T430_OranC : '',
                    'suspansiyon_arka_2' => isset($suspansiyon) ? $suspansiyon?->T430_OranD : '',
                    'el_freni_dengesizlik_orani' => isset($getBrake) ? $getBrake?->T420_elfrenidngszOran : '',
                    'getNotes' => json_decode(json_encode($notlar), true),
                    'is_closed' => 1,
                ];
            }
            elseif ($_GET['type'] == 'bodywork'){
                $userID = DB::connection('mysql2')->table('T401_SRVTESTLERI')->where('T401_servis_UQ',$_GET['expertise_id'])->where('T401_bolum_kodu',4)->first();
                $getBodyworks = [];
                $resim = DB::connection('mysql2')->table('T403_SRVRESIM')->where('T403_servis_UQ',$_GET['expertise_id'])->first();
                $notlar = DB::connection('mysql2')->table('T402_SRVNOTLARI')->where('T402_servis_UQ',$_GET['expertise_id'])->where('T402_test_kodu',4)->select('T402_aciklama as note')->get();
                foreach (__('arrays.bodyworks') as $key => $bodywork){
                    $getBodyworkKod = DB::connection('mysql2')->table('T116_TESTALANLARI')->where('T116_aciklama','LIKE',"%{$bodywork}%")->first();
                    $getBodywork = DB::connection('mysql2')->table('T440_KAPORTA')->where('T440_servis_UQ',$_GET['expertise_id'])->where('T440_alan_kodu',$getBodyworkKod->T116_kod)->first();
                    $getBodyworks[$key] = [
                        'orijinal' => $getBodywork?->T440_orjinal ?? 0,
                        'boyali' => $getBodywork?->T440_boya ?? 0,
                        'degisen' => $getBodywork?->T440_degisen ?? 0,
                        'duz' => $getBodywork?->T440_duz ?? 0,
                        'note' => $getBodywork?->T440_aciklama ?? '',
                        'title'=>$bodywork
                    ];
                }
                $expertise = [
                    'is_closed' => 1,
                    'uuid' => $oldDB->T400_UQ,
                    'kaporta_kontrol_user' => $userID?->T401_kullanici_UQ,
                    'plaka' => $car->T202_plakano,
                    'sase_no' => $car->T202_saseno,
                    'belge_no' => $oldDB->T400_belgeno,
                    'date' => $getBodywork ? \Carbon\Carbon::make($oldDB->T400_belgetarihi)->format('Y-m-d H:i:s') : now()->format('Y-m-d H:i:s'),
                    'car_model' => $model->T107_aciklama ?? '',
                    'getStocks' =>$stocks,
                    'km' => $oldDB->T400_arackm,
                    'getBodyworks'=>$getBodyworks,
                    'getBodyworkNotes'=>json_decode(json_encode($notlar), true),
                    'bodywork_image' => 'data:image/jpeg;base64,'.base64_encode($resim?->T403_resim),
                    'created_at'=>'2023-05-28 00:00:00',
                    'coordinates' => []
                ];
            }
            elseif ($_GET['type'] == 'diagnostic'){
                $userID = DB::connection('mysql2')->table('T401_SRVTESTLERI')->where('T401_servis_UQ',$_GET['expertise_id'])->where('T401_bolum_kodu',6)->first();
                $notlar = DB::connection('mysql2')->table('T402_SRVNOTLARI')->where('T402_servis_UQ',$_GET['expertise_id'])->where('T402_test_kodu',6)->select('T402_aciklama as note')->get();
                $expertise = [
                    'is_closed' => 1,
                    'uuid' => $oldDB->T400_UQ,
                    'diagnostic_kontrol_user' => $userID?->T401_kullanici_UQ,
                    'diagnostic_file' => '',
                    'plaka' => $car->T202_plakano,
                    'sase_no' => $car->T202_saseno,
                    'belge_no' => $oldDB->T400_belgeno,
                    'date' => \Carbon\Carbon::make($oldDB->T400_belgetarihi)->format('Y-m-d H:i:s') ?? now()->format('Y-m-d H:i:s'),
                    'car_model' => $model->T107_aciklama ?? '',
                    'getStocks' =>$stocks,
                    'km' => $oldDB->T400_arackm,
                    'getDiagnostics' =>json_decode(json_encode($notlar), true)
                ];
            }
            elseif ($_GET['type'] == 'internal_control'){
                $userID = DB::connection('mysql2')->table('T401_SRVTESTLERI')->where('T401_servis_UQ',$_GET['expertise_id'])->where('T401_bolum_kodu',8)->first();
                $getInternalControls = [];
                foreach (__('arrays.internal_controls') as $key => $internalControl){
                    $getInternalControlKod = DB::connection('mysql2')->table('T116_TESTALANLARI')->where('T116_aciklama','LIKE',"%{$internalControl}%")->where('T116_grupno',2)->first();
                    $getInternalControl = DB::connection('mysql2')->table('T451_MEKANIKTSE')->where('T451_servis_UQ',$_GET['expertise_id'])->where('T451_alan_kodu',$getInternalControlKod->T116_kod ?? 0)->first();
                    $getInternalControls[$key] = [
                        'sorunlu_mu' => $getInternalControl?->T451_sonuc_kodu == 1050 ? 1 : 0,
                        'note' => $getInternalControl?->T451_aciklama ?? '',
                        'title'=>$internalControl
                    ];
                }
                $expertise = [
                    'is_closed' => 1,
                    'uuid' => $oldDB->T400_UQ,
                    'ic_kontrol_user' => $userID?->T401_kullanici_UQ,
                    'plaka' => $car->T202_plakano,
                    'sase_no' => $car->T202_saseno,
                    'belge_no' => $oldDB->T400_belgeno,
                    'date' => $getInternalControl ? \Carbon\Carbon::make($oldDB->T400_belgetarihi)->format('Y-m-d H:i:s') : now()->format('Y-m-d H:i:s'),
                    'car_model' => $model->T107_aciklama ?? '',
                    'getStocks' =>$stocks,
                    'km' => $oldDB->T400_arackm,
                    'getInternalControls' => $getInternalControls
                ];
            }
            elseif ($_GET['type'] == 'tire_and_rim'){
                $userID = DB::connection('mysql2')->table('T401_SRVTESTLERI')->where('T401_servis_UQ',$_GET['expertise_id'])->where('T401_bolum_kodu',10)->first();
                $notlar = DB::connection('mysql2')->table('T402_SRVNOTLARI')->where('T402_servis_UQ',$_GET['expertise_id'])->where('T402_test_kodu',10)->select('T402_aciklama as note')->get();
                $getTireAndRims = [];
                foreach (__('arrays.tire_and_rims') as $key => $tireAndRim){
                    $getTireAndRimKod = DB::connection('mysql2')->table('T116_TESTALANLARI')->where('T116_aciklama','LIKE',"%{$tireAndRim}%")->where('T116_grupno',4)->first();
                    $getTireAndRim = DB::connection('mysql2')->table('T451_MEKANIKTSE')->where('T451_servis_UQ',$_GET['expertise_id'])->where('T451_alan_kodu',$getTireAndRimKod->T116_kod ?? 0)->first();
                    $getTireAndRims[$key] = [
                        'sorunlu_mu' => $getTireAndRim?->T451_sonuc_kodu == 1050 ? 1 : 0,
                        'note' => $getTireAndRim?->T451_aciklama ?? '',
                        'dis' => $getTireAndRim?->T451_deger_1 ?? '',
                        'basinc' => $getTireAndRim?->T451_deger_2 ?? '',
                        'title'=>$tireAndRim
                    ];
                }
                $expertise = [
                    'is_closed' => 1,
                    'uuid' => $oldDB->T400_UQ,
                    'lastik_kontrol_user' => $userID?->T401_kullanici_UQ,
                    'plaka' => $car->T202_plakano,
                    'sase_no' => $car->T202_saseno,
                    'belge_no' => $oldDB->T400_belgeno,
                    'date' => $getTireAndRim ? \Carbon\Carbon::make($oldDB->T400_belgetarihi)->format('Y-m-d H:i:s') : now()->format('Y-m-d H:i:s'),
                    'car_model' => $model->T107_aciklama ?? '',
                    'getStocks' =>$stocks,
                    'km' => $oldDB->T400_arackm,
                    'getTireAndRims' => $getTireAndRims,
                    'getTireAndRimNotes' => json_decode(json_encode($notlar), true)
                ];
            }
            elseif ($_GET['type'] == 'sub_controls_and_engine'){
                $userID = DB::connection('mysql2')->table('T401_SRVTESTLERI')->where('T401_servis_UQ',$_GET['expertise_id'])->where('T401_bolum_kodu',9)->first();
                $notlar = DB::connection('mysql2')->table('T402_SRVNOTLARI')->where('T402_servis_UQ',$_GET['expertise_id'])->where('T402_test_kodu',9)->select('T402_aciklama as note')->get();
                $getSubControlsAndEngines = [];

                foreach (__('arrays.sub_controls_and_engines') as $key => $subControlEngine){
                    $getSubControlAndEngineKod = DB::connection('mysql2')->table('T116_TESTALANLARI')->where('T116_aciklama','LIKE',"%{$subControlEngine}%")->where('T116_test_kodu',8)->whereIn('T116_grupno',[1,3])->first();
                    $getSubControlAndEngine = DB::connection('mysql2')->table('T451_MEKANIKTSE')->where('T451_servis_UQ',$_GET['expertise_id'])->where('T451_alan_kodu',$getSubControlAndEngineKod->T116_kod ?? 0)->first();

                    if ($getSubControlAndEngine?->T451_sonuc_kodu == 1030)
                        $status = 1;
                    elseif ($getSubControlAndEngine?->T451_sonuc_kodu == 1020)
                        $status = 2;
                    elseif ($getSubControlAndEngine?->T451_sonuc_kodu == 1010)
                        $status = 3;
                    elseif ($getSubControlAndEngine?->T451_sonuc_kodu == 1110)
                        $status = 4;
                    elseif ($getSubControlAndEngine?->T451_sonuc_kodu == 1070)
                        $status = 5;
                    elseif ($getSubControlAndEngine?->T451_sonuc_kodu == 1080)
                        $status = 6;
                    elseif ($getSubControlAndEngine?->T451_sonuc_kodu == 1040)
                        $status = 7;
                    elseif ($getSubControlAndEngine?->T451_sonuc_kodu == 1050)
                        $status = 8;
                    else
                        $status = 0;
                    $getSubControlsAndEngines[$key] = [
                        'status' => $status,
                        'note' => $getSubControlAndEngine?->T451_aciklama ?? '',
                        'title'=>$subControlEngine
                    ];
                }

                $expertise = [
                    'is_closed' => 1,
                    'uuid' => $oldDB->T400_UQ,
                    'alt_motor_kontrol_user' => $userID?->T401_kullanici_UQ,
                    'plaka' => $car->T202_plakano,
                    'sase_no' => $car->T202_saseno,
                    'belge_no' => $oldDB->T400_belgeno,
                    'date' => $getSubControlAndEngine ? \Carbon\Carbon::make($oldDB->T400_belgetarihi)->format('Y-m-d H:i:s') : now()->format('Y-m-d H:i:s'),
                    'car_model' => $model->T107_aciklama ?? '',
                    'getStocks' =>$stocks,
                    'km' => $oldDB->T400_arackm,
                    'getSubControlsAndEngines' => $getSubControlsAndEngines,
                    'getSubControlsAndEngineNotes' => json_decode(json_encode($notlar), true),

                    'co2_test' => false,
                ];
            }
            elseif ($_GET['type'] == 'component'){
                $userID = DB::connection('mysql2')->table('T401_SRVTESTLERI')->where('T401_servis_UQ',$_GET['expertise_id'])->where('T401_bolum_kodu',11)->first();
                $getComponents = [];
                $notlar = DB::connection('mysql2')->table('T402_SRVNOTLARI')->where('T402_servis_UQ',$_GET['expertise_id'])->where('T402_test_kodu',11)->select('T402_aciklama as note')->get();
                foreach (__('arrays.components') as $key => $component){
                    $getComponentKod = DB::connection('mysql2')->table('T116_TESTALANLARI')->where('T116_aciklama',$component)->where('T116_test_kodu',8)->whereIn('T116_grupno',[3,6])->first();
                    $getComponent = DB::connection('mysql2')->table('T451_MEKANIKTSE')->where('T451_servis_UQ',$_GET['expertise_id'])->where('T451_alan_kodu',$getComponentKod->T116_kod ?? 0)->first();
                    if ($getComponent?->T451_sonuc_kodu == 1060)
                        $status = 4;
                    elseif ($getComponent?->T451_sonuc_kodu == 1030)
                        $status = 1;
                    elseif ($getComponent?->T451_sonuc_kodu == 1010)
                        $status = 3;
                    elseif ($getComponent?->T451_sonuc_kodu == 1120)
                        $status = 5;
                    elseif ($getComponent?->T451_sonuc_kodu == 1020)
                        $status = 2;
                    else
                        $status = 0;

                    $getComponents[$key] = [
                        'status' => $status,
                        'note' => $getInternalcontrol?->note ?? '',
                        'title'=>$component
                    ];
                }

                $expertise = [
                    'is_closed' => 1,
                    'uuid' => $oldDB->T400_UQ,
                    'komponent_kontrol_user' => $userID?->T401_kullanici_UQ,
                    'plaka' => $car->T202_plakano,
                    'sase_no' => $car->T202_saseno,
                    'belge_no' => $oldDB->T400_belgeno,
                    'date' => $getComponent ? \Carbon\Carbon::make($oldDB->T400_belgetarihi)->format('Y-m-d H:i:s') : now()->format('Y-m-d H:i:s'),
                    'car_model' => $model->T107_aciklama ?? '',
                    'getStocks' =>$stocks,
                    'km' => $oldDB->T400_arackm,
                    'getComponents' => $getComponents,
                    'getComponentNotes' => json_decode(json_encode($notlar), true)
                ];
            }
        }else{
            return redirect()->route('expertises.index')->with('error','Kayıt Bulunamadı!');
        }

        $expertise['islemler'] = $islemler;
        $expertise['unlock_tabs'] = $unlock_tabs;

        return view('pages.expertise.' . $_GET['type'], compact(['expertise', 'pageTitle']));
    }

    public function updateExpertise(Request $request)
    {
        /** @var User $user */
        $user = auth()->user();

        if (!$user->getUserRoleGroup
            || !$user->getUserRoleGroup->getRoles->whereIn('key', ['edit_expertise'])->first()
        ) {
            return redirect()->route('index')->with('error', 'Yetkiniz yok!');
        }

        $expertise = Expertise::where('uuid', $request->get('expertise_id'))->first();

        if (!$expertise) {
            return back()->with('error', 'Eski kayıtlar düzenlenemez!');
        }

        if ($expertise->ftp_ok == 1 && !$user->isAdmin()) {
            return back()->with('error', 'Bu ekspertiz işleme kapanmıştır!');
        }

        $return_url = '';

        if ($request->type == 'car') {
            if($user->user_role_group_id == 33 || $user->user_role_group_id == 34){
                $expertise->km = (int)$request->km;
                $expertise->sase_no = strtoupper($request->sase_no);
                $expertise->km_type = (int)$request->km_type;

                $car = Car::where('id',$expertise->car_id)->first();
                $car->km = (int)$request->km;
                $car->sase_no = strtoupper($request->sase_no);
                $car->km_type = (int)$request->km_type;
                $car->save();
            }else{
                $model = CarGroup::where('id',$request->car_group_model_id)->first();
                $car = Car::where('id',$expertise->car_id)->first();
                $getParentGroup = $model->getParentGroup;
                $car->km = (int)$request->km;
                $car->km_type = (int)$request->km_type;
                $car->car_group_tip_id = !empty($getParentGroup->getParentGroup->id) ? $getParentGroup->getParentGroup->id : 0;
                $car->car_group_marka_id = $getParentGroup->id;
                $car->car_group_model_id = $request->car_group_model_id;
                $car->plaka = $request->plaka;
                $car->sase_no = strtoupper($request->sase_no);
                $car->car_case_type_id = $request->car_case_type_id;
                $car->car_fuels_id = $request->car_fuels_id;
                $car->car_gears_id = $request->car_gears_id;
                $car->cekis = $request->cekis;
                $car->motor_hacmi = $request->motor_hacmi;
                $car->model_yili = $request->model_yili;
                $car->save();
                logRecord(
                    "edit_car",
                    $user->name." $car->plaka plakalı aracın kilometresini ".(int)$request->km ." olarak güncelledi.",
                    $car->id
                );
                logRecord(
                    "update_expertise",
                    $user->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydının araç kontrolünü üstlendi.",
                    $expertise->id
                );
                $expertise->km = (int)$request->km;
                $expertise->sase_no = strtoupper($request->sase_no);
                $expertise->km_type = (int)$request->km_type;

                if (!$user->isAdmin()) {
                    $expertise->arac_kontrol_user = $user->id;
                }

                $arac_kontrol = null;

                $eksikAlanlar = [];

                if (empty($request->km)) {
                    $eksikAlanlar[] = 'Km';
                }
                if (empty($request->km_type)) {
                    $eksikAlanlar[] = 'Km türü';
                }
                if (empty($request->car_group_model_id)) {
                    $eksikAlanlar[] = 'Araç modeli';
                }
                if (empty($request->plaka)) {
                    $eksikAlanlar[] = 'Plaka';
                }
                if (empty($request->sase_no)) {
                    $eksikAlanlar[] = 'Şase no';
                }
                if (empty($request->car_case_type_id)) {
                    $eksikAlanlar[] = 'Araç Kasa Tipi';
                }
                if (empty($request->car_fuels_id)) {
                    $eksikAlanlar[] = 'Yakıt Türü';
                }
                if (empty($request->car_gears_id)) {
                    $eksikAlanlar[] = 'Vites Türü';
                }
                if (empty($request->cekis)) {
                    $eksikAlanlar[] = 'Çekiş';
                }
                if (empty($request->motor_hacmi)) {
                    $eksikAlanlar[] = 'Motor Hacmi';
                }
                if (empty($request->model_yili)) {
                    $eksikAlanlar[] = 'Model Yılı';
                }

                if (empty($eksikAlanlar)) {
                    $arac_kontrol = 1;
                } else {
                    $arac_kontrol = 0;
                }

                if ($expertise->ftp_ok == 1)
                    $expertise->arac_kontrol = 1;
                else
                    $expertise->arac_kontrol = $arac_kontrol;
            }

            $expertise->save();
            if (!empty($eksikAlanlar)){
                $eksikMesaj = 'Eksik alanlar: ' . implode(', ', $eksikAlanlar);
                return response()->json(['success' => false, 'message' => $eksikMesaj,'return_url'=>'','type'=>'error']);
            }
        } elseif ($request->type == 'brake') {
            try {
                $this->expertiseBrakeService->save($request, $expertise);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'return_url' => '',
                    'type' => 'error'
                ]);
            }

            logRecord(
                "update_expertise",
                $user->name . " adlı kullanıcı " . $expertise->getCari?->unvan . " adlı müşterinin ekspertiz kaydının fren kontrolünü üstlendi.",
                $expertise->id
            );

            if (!$user->isAdmin()) {
                $expertise->fren_kontrol_user = $user->id;
            }

            $errors = [];

            if (!$request->filled('yanal_kayma_on')) {
                $errors[] = 'Ön Yanal Kayma';
            }
            if (!$request->filled('date')) {
                $errors[] = 'Tarih';
            }
            if (!$request->filled('yanal_kayma_arka')) {
                $errors[] = 'Arka Yanal Kayma';
            }
            if (!$request->filled('max_kuvvet_on_1')) {
                $errors[] = 'Maksimum Kuvvet Sol Ön';
            }
            if (!$request->filled('max_kuvvet_on_2')) {
                $errors[] = 'Maksimum Kuvvet Sağ Ön';
            }
            if (!$request->filled('max_kuvvet_arka_1')) {
                $errors[] = 'Maksimum Kuvvet Sol Arka';
            }
            if (!$request->filled('max_kuvvet_arka_2')) {
                $errors[] = 'Maksimum Kuvvet Sağ Arka';
            }
            if (!$request->filled('dengesizlik_orani_on')) {
                $errors[] = 'Dengesizlik Oranı Ön';
            }
            if (!$request->filled('dengesizlik_orani_arka')) {
                $errors[] = 'Dengesizlik Oranı Arka';
            }
            if (!$request->filled('yalpa_orani_on_1')) {
                $errors[] = 'Yalpa Oranı Sol Ön';
            }
            if (!$request->filled('yalpa_orani_on_2')) {
                $errors[] = 'Yalpa Oranı Sağ Ön';
            }
            if (!$request->filled('yalpa_orani_arka_1')) {
                $errors[] = 'Yalpa Oranı Sol Arka';
            }
            if (!$request->filled('yalpa_orani_arka_2')) {
                $errors[] = 'Yalpa Oranı Sağ Arka';
            }
            if (!$request->filled('suspansiyon_on_1')) {
                $errors[] = 'Sol Ön Süspansiyon';
            }
            if (!$request->filled('suspansiyon_on_2')) {
                $errors[] = 'Sağ Ön Süspansiyon';
            }
            if (!$request->filled('suspansiyon_arka_1')) {
                $errors[] = 'Sol Arka Süspansiyon';
            }
            if (!$request->filled('suspansiyon_arka_2')) {
                $errors[] = 'Sağ Arka Süspansiyon';
            }
            if (!$request->filled('el_freni_dengesizlik_orani')) {
                $errors[] = 'El Freni Dengesizlik Oranı';
            }

            $brakeExpertControl = 0;

            if (empty($errors) || $expertise->ftp_ok == 1) {
                $brakeExpertControl = 1;
            }

            try {
                $expertise->fren_kontrol = $brakeExpertControl;
                $expertise->save();

                if (!empty($errors)) {
                    throw new \Exception('Eksik alanlar: ' . implode('<br>', $errors));
                }
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'return_url' => '',
                    'type' => 'error'
                ]);
            }
        } elseif ($request->type == 'bodywork') {
            if(!empty($user->user_role_group_id) && ($user->user_role_group_id == 34)){
                return back()->with('error','Yetkiniz Yok');
            }

            if ($request->canvas){
                $data = substr($request->canvas, strpos($request->canvas, ',') + 1);

                $data = base64_decode($data);
                if ($expertise->bodywork_image)
                    $path = $expertise->bodywork_image;
                else
                    $path = 'bodywork/'.uuid_create().".webp";
                $path = 'bodywork/'.uuid_create().".webp";
                Storage::disk('public2')->put($path, $data);
                $expertise->bodywork_image = $path;
                $expertise->save();
            }

            // Tüm mevcut bodywork kayıtlarını tek bir sorguyla çekiyoruz
            $existingBodyworks = ExpertiseBodywork::where('expertise_id', $expertise->id)->get()->keyBy('key');

            $insertData = [];
            $updateData = [];
            $updateKeys = [];

            foreach ($request->all() as $key => $item) {
                if ($key != '_token' && $key != 'expertise_id' && $key != 'type' && $key != 'date' && $key != 'canvas' && $key != 'not' && $key != 'kaporta_kontrol_user' && $key != 'submit' && $key != 'close') {
                    // Mevcut kaydı $existingBodyworks'ten alıyoruz
                    $bodywork = $existingBodyworks->get($key);

                    $data = [
                        'expertise_id' => $expertise->id,
                        'key' => $key,
                        'orijinal' => in_array('orijinal', $item) ? 1 : 0,
                        'boyali' => in_array('boyali', $item) ? 1 : 0,
                        'degisen' => in_array('degisen', $item) ? 1 : 0,
                        'duz' => in_array('duz', $item) ? 1 : 0,
                        'note' => $item[array_key_last($item)],
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    if ($bodywork) {
                        // Güncellenecek kayıtlar
                        $updateKeys[] = $bodywork->id;
                        unset($data['created_at']);
                        $updateData[] = $data;
                    } else {
                        // Eklenecek kayıtlar
                        $insertData[] = $data;
                    }
                }
            }

            if (!empty($insertData)) {
                ExpertiseBodywork::insert($insertData);
            }

            if (!empty($updateData)) {
                $updateQuery = "UPDATE expertise_bodyworks SET ";
                $updateColumns = ['orijinal', 'boyali', 'degisen', 'duz', 'note','updated_at'];

                foreach ($updateColumns as $column) {
                    $updateQuery .= "$column = CASE id ";
                    foreach ($updateData as $index => $data) {
                        $updateQuery .= "WHEN {$updateKeys[$index]} THEN '{$data[$column]}' ";
                    }
                    $updateQuery .= "END, ";
                }

                // Fazladan virgül ve boşlukları temizleyelim
                $updateQuery = rtrim($updateQuery, ', ') . " WHERE id IN (" . implode(',', $updateKeys) . ")";

                DB::statement($updateQuery);
            }

            logRecord(
                "update_expertise",
                $user->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydının kaporta kontrolünü üstlendi.",
                $expertise->id
            );

            if (!$user->isAdmin()) {
                $expertise->kaporta_kontrol_user = $user->id;
            }

            $eksikAlanlar = [];
            $kaportaKontrol = 1;

            $formData = $request->all();
            foreach ($formData as $key => $value) {
                if (in_array($key, array_keys(__('arrays.bodyworks')))) {
                    if (!in_array('orijinal', $value)
                        && !in_array('boyali', $value)
                        && !in_array('degisen', $value)
                        && !in_array('duz', $value)
                        && $value[array_key_last($value)] == ''
                    ) {
                        $eksikAlanlar[] = ucfirst(str_replace('_', ' ', $key)) . ' alanı eksik.';
                        $kaportaKontrol = 0;
                    }
                }
            }

            if ($expertise->ftp_ok == 1)
                $expertise->kaporta_kontrol = 1;
            else
                $expertise->kaporta_kontrol = $kaportaKontrol;

            $expertise->save();
            if (!empty($eksikAlanlar)){
                $eksikMesaj = implode('<br>', $eksikAlanlar);
                // Eksik alanlar hakkında kullanıcıya bilgi vermek için
                return response()->json(['success' => false, 'message' => $eksikMesaj,'return_url'=>'','type'=>'error']);
            }
        } elseif ($request->type == 'diagnostic') {
            logRecord(
                "update_expertise",
                $user->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydının diagnostic kontrolünü düzenledi.",
                $expertise->id
            );

            if (!$user->isAdmin()) {
                $expertise->diagnostic_kontrol_user = $user->id;
            }

            if ($request->hasFile('file')) {
                $expertise->diagnostic_kontrol = 1;
            }elseif (ExpertiseDiagnosticNote::where('expertise_id',$expertise->id)->exists())
                $expertise->diagnostic_kontrol = 1;
            else{
                return response()->json(['success' => false, 'message' => "Kayıt İçin Dosya veya En az 1 not eklenmesi gerekmektedir.",'return_url'=>'','type'=>'error']);
            }

            if($request->hasFile('file'))
                $expertise->diagnostic_file = $request->file('file')->store('diagnostic','public2');

            $expertise->save();
        } elseif ($request->type == 'internal_control') {
            // Mevcut kayıtları tek bir sorgu ile çekiyoruz
            $existingInternalControls = ExpertiseInternalControl::where('expertise_id', $expertise->id)->get()->keyBy('key');

            $insertData = [];
            $updateData = [];
            $updateKeys = [];

            foreach ($request->all() as $key => $item) {
                if ($key != '_token' && $key != 'expertise_id' && $key != 'type' && $key != 'date' && $key != 'ic_kontrol_user' && $key != 'submit' && $key != 'close') {
                    // Mevcut kaydı $existingInternalControls'ten alıyoruz
                    $internalControl = $existingInternalControls->get($key);

                    $data = [
                        'expertise_id' => $expertise->id,
                        'key' => $key,
                        'answer' => $item[0],
                        'note' => $item[1] ?? '',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    if ($internalControl) {
                        // Güncellenecek kayıtlar
                        $updateKeys[] = $internalControl->id;
                        unset($data['created_at']);
                        $updateData[] = $data;
                    } else {
                        // Eklenecek kayıtlar
                        $insertData[] = $data;
                    }
                }
            }

// Toplu ekleme işlemi
            if (!empty($insertData)) {
                try {
                    ExpertiseInternalControl::insert($insertData);
                } catch (\Exception $e) {
                    return response()->json([
                        'success' => false,
                        'message' => $e->getMessage(),
                        'return_url' => '',
                        'type' => 'error'
                    ]);
                }
            }

// Toplu güncelleme işlemi
            if (!empty($updateData)) {
                $updateQuery = "UPDATE expertise_internal_controls SET ";
                $updateColumns = ['answer', 'note','updated_at'];

                foreach ($updateColumns as $column) {
                    $updateQuery .= "$column = CASE id ";
                    foreach ($updateData as $index => $data) {
                        $dataColumn = addslashes($data[$column]);
                        $updateQuery .= "WHEN {$updateKeys[$index]} THEN '{$dataColumn}' ";
                    }
                    $updateQuery .= "END, ";
                }

                // Fazladan virgül ve boşlukları temizleyelim
                $updateQuery = rtrim($updateQuery, ', ') . " WHERE id IN (" . implode(',', $updateKeys) . ")";

                try {
                    DB::statement($updateQuery);
                } catch (\Exception $e) {
                    return response()->json([
                        'success' => false,
                        'message' => $e->getMessage(),
                        'return_url' => '',
                        'type' => 'error'
                    ]);
                }
            }

            logRecord(
                "update_expertise",
                $user->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydının iç kontrolünü üstlendi.",
                $expertise->id
            );

            if (!$user->isAdmin()) {
                $expertise->ic_kontrol_user = $user->id;
            }

            $eksikAlanlar = [];
            $ic_kontrol = 1;

            foreach ($request->all() as $key => $item) {
                if (in_array($key, array_keys(__('arrays.internal_controls')))
                    && !in_array('sorunsuz', $item)
                    && !in_array('sorunlu', $item)
                    && !in_array('yok', $item)
                    && $item[array_key_last($item)] == ''
                ) {
                    $eksikAlanlar[] = ucfirst(str_replace('_', ' ', $key)) . ' alanı eksik.';
                    $ic_kontrol = 0;
                }
            }

            if ($expertise->ftp_ok == 1) {
                $expertise->ic_kontrol = 1;
            } else {
                $expertise->ic_kontrol = $ic_kontrol;
            }

            try {
                $expertise->save();
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'return_url' => '',
                    'type' => 'error'
                ]);
            }

            if (!empty($eksikMesaj)){
                $eksikMesaj = implode('<br>', $eksikAlanlar);
                // Eksik alanlar hakkında kullanıcıya bilgi vermek için
                return response()->json(['success' => false, 'message' => $eksikMesaj,'return_url'=>'','type'=>'error']);
            }

        } elseif ($request->type == 'tire_and_rim') {
            // Mevcut kayıtları tek bir sorgu ile çekiyoruz
            $existingTireAndRims = ExpertiseTireAndRim::where('expertise_id', $expertise->id)->get()->keyBy('key');

            $insertData = [];
            $updateData = [];
            $updateKeys = [];

            foreach ($request->all() as $key => $item) {
                if ($key != '_token' && $key != 'expertise_id' && $key != 'type' && $key != 'date' && $key != 'not' && $key != 'lastik_kontrol_user' && $key != 'submit' && $key != 'close') {
                    // Mevcut kaydı $existingTireAndRims'ten alıyoruz
                    $tireAndRim = $existingTireAndRims->get($key);

                    $data = [
                        'expertise_id' => $expertise->id,
                        'key' => $key,
                        'sorunlu_mu' => $item[0],
                        'note' => $item[1],
                        'dis' => (float)str_replace(',', '.', $item[2]),
                        'basinc' => (float)str_replace(',', '.', $item[3]),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    if ($tireAndRim) {
                        // Güncellenecek kayıtlar
                        $updateKeys[] = $tireAndRim->id;
                        unset($data['created_at']);
                        $updateData[] = $data;
                    } else {
                        // Eklenecek kayıtlar
                        $insertData[] = $data;
                    }
                }
            }

// Toplu ekleme işlemi
            if (!empty($insertData)) {
                ExpertiseTireAndRim::insert($insertData);
            }

            if (!empty($updateData)) {
                $updateQuery = "UPDATE expertise_tire_and_rims SET ";
                $updateColumns = ['sorunlu_mu', 'note', 'dis', 'basinc', 'updated_at'];
                $setClauses = [];

                foreach ($updateColumns as $column) {
                    $columnCases = [];
                    foreach ($updateData as $index => $data) {
                        // Değerin boş ya da null olmadığını kontrol et
                        if (isset($data[$column]) && $data[$column] !== '') {
                            $value = $data[$column] === null ? 'NULL' : "'{$data[$column]}'";
                            $columnCases[] = "WHEN {$updateKeys[$index]} THEN $value";
                        }
                    }

                    // Eğer bu sütun için herhangi bir değer eklenmişse, CASE ifadelerini ekle
                    if (!empty($columnCases)) {
                        $setClauses[] = "$column = CASE id " . implode(" ", $columnCases) . " END";
                    }
                }

                // Sadece dolu olan set ifadelerini sorguya dahil edelim
                if (!empty($setClauses)) {
                    $updateQuery .= implode(', ', $setClauses) . " WHERE id IN (" . implode(',', $updateKeys) . ")";
                    DB::statement($updateQuery);
                }
            }


            logRecord(
                "update_expertise",
                $user->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydının lastik ve jant kontrolünü üstlendi.",
                $expertise->id
            );

            if (!$user->isAdmin()) {
                $expertise->lastik_jant_kontrol_user = $user->id;
            }

            $eksikAlanlar = [];

            foreach ($expertise['getTireAndRims'] as $index => $tireAndRim) {
                if (empty($tireAndRim['dis'])) {
                    $eksikAlanlar[] = "Lastik ve Jant {$index}. sıradaki 'dis' alanı eksik";
                }
                if (empty($tireAndRim['basinc'])) {
                    $eksikAlanlar[] = "Lastik ve Jant {$index}. sıradaki 'basinc' alanı eksik";
                }
            }

            if (empty($eksikMesaj) || $expertise->ftp_ok == 1){
                $expertise->lastik_jant_kontrol = 1;
                $expertise->save();
            }else{
                $eksikMesaj = 'Eksik alanlar: ' . implode(', ', $eksikAlanlar);
                return response()->json(['success' => false, 'message' => $eksikMesaj,'return_url'=>'','type'=>'error']);
            }
        } elseif ($request->type == 'sub_controls_and_engine') {
            if(!empty($user->user_role_group_id) && $user->user_role_group_id == 33){
                return back()->with('error','Yetkiniz Yok');
            }

            // Mevcut kayıtları tek bir sorgu ile çekiyoruz
            $existingSubControlAndEngines = ExpertiseSubControlAndEngine::where('expertise_id', $expertise->id)->get()->keyBy('key');

            $insertData = [];
            $updateData = [];
            $updateKeys = [];

            foreach ($request->all() as $key => $item) {
                if ($key != '_token' && $key != 'expertise_id' && $key != 'type' && $key != 'date' && $key != 'not' && $key != 'alt_motor_kontrol_user' && $key != 'submit' && $key != 'close') {
                    // Mevcut kaydı $existingSubControlAndEngines'ten alıyoruz
                    $subControlAndEngine = $existingSubControlAndEngines->get($key);

                    $data = [
                        'expertise_id' => $expertise->id,
                        'key' => $key,
                        'status' => 0,
                        'note' => isset($item[1]) ? $item[1] : '',
                        'answer' => isset($item[0]) ? $item[0] : '',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    if ($subControlAndEngine) {
                        // Güncellenecek kayıtlar
                        $updateKeys[] = $subControlAndEngine->id;
                        unset($data['created_at']);
                        $updateData[] = $data;
                    } else {
                        // Eklenecek kayıtlar
                        $insertData[] = $data;
                    }
                }
            }

// Toplu ekleme işlemi
            if (!empty($insertData)) {
                ExpertiseSubControlAndEngine::insert($insertData);
            }

// Toplu güncelleme işlemi
            if (!empty($updateData)) {
                $updateQuery = "UPDATE expertise_sub_control_and_engines SET ";
                $updateColumns = ['status', 'note', 'answer','updated_at'];

                foreach ($updateColumns as $column) {
                    $updateQuery .= "$column = CASE id ";
                    foreach ($updateData as $index => $data) {
                        $updateQuery .= "WHEN {$updateKeys[$index]} THEN '{$data[$column]}' ";
                    }
                    $updateQuery .= "END, ";
                }

                // Fazladan virgül ve boşlukları temizleyelim
                $updateQuery = rtrim($updateQuery, ', ') . " WHERE id IN (" . implode(',', $updateKeys) . ")";

                DB::statement($updateQuery);
            }


            logRecord(
                "update_expertise",
                $user->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydının alt kontroller ve motor kontrolünü üstlendi.",
                $expertise->id
            );

            if (!$user->isAdmin()) {
                $expertise->alt_motor_kontrol_user = $user->id;
            }

            $eksikAlanlar = [];
            $allSelected = true;

            foreach ($expertise['getSubControlsAndEngines'] as $key => $bodywork) {
                if ($key != "co2_kacak_testi" && empty($bodywork['answer'])) {
                    $eksikAlanlar[] = ucfirst(str_replace('_', ' ', $key)) . ' alanı eksik.';
                    $allSelected = false;
                }
            }

            if ($allSelected || $expertise->ftp_ok == 1) {
                $alt_motor_kontrol = 1;
            } else {
                $alt_motor_kontrol = 0;

            }

            $expertise->alt_motor_kontrol = $alt_motor_kontrol;

            $expertise->save();

            if (!empty($eksikMesaj)){
                $eksikMesaj = implode('<br>', $eksikAlanlar);
                // Eksik alanlar hakkında kullanıcıya bilgi vermek için
                return response()->json(['success' => false, 'message' => $eksikMesaj,'return_url'=>'','type'=>'error']);
            }

        } elseif ($request->type == 'component') {
            try {
                $this->expertiseComponentService->save($request, $expertise);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'return_url' => '',
                    'type' => 'error'
                ]);
            }

            $component_dyno = ComponentDyno::where('expertise_id',$expertise->id)->first();
            if ($component_dyno && $component_dyno->type == 'auto'){
                return response()->json(['success'=>'false','message'=>'Veriler Cihazdan Aktarıldığı İçin İşlem Yapılamaz!']);
            }elseif($expertise->getBranch?->dyno_values_auto == 0){
                if(empty($component_dyno)){
                    $component_dyno = new ComponentDyno();
                    $component_dyno->expertise_id = $expertise->id;
                }
                $component_dyno->measured_kw = (float)replaceFloat($request->measured_kw);
                $component_dyno->measured_hp = (float)replaceFloat($request->measured_hp);
                $component_dyno->calculated_kw = (float)replaceFloat($request->calculated_kw);
                $component_dyno->calculated_hp = (float)replaceFloat($request->calculated_hp);
                $component_dyno->calculated_rpm = (float)replaceFloat($request->calculated_rpm);
                $component_dyno->transfer_kw = (float)replaceFloat($request->transfer_kw);
                $component_dyno->transfer_hp = (float)replaceFloat($request->transfer_hp);
                $component_dyno->transfer_rpm = (float)replaceFloat($request->transfer_rpm);
                $component_dyno->save();
            }

            logRecord(
                "update_expertise",
                $user->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydının komponent kontrolünü üstlendi.",
                $expertise->id
            );

            if (!$user->isAdmin()) {
                $expertise->komponent_kontrol_user = $user->id;
            }

            $errors = [];

            foreach ($request->all() as $key => $value) {
                if (strpos($key, '[]') !== false && is_array($value)) {
                    foreach ($value as $selectValue) {
                        if (empty($selectValue)) {
                            $errors[] = ucfirst(str_replace('_', ' ', $key)) . ' seçimi eksik.';
                        }
                    }
                }
            }

            if (!isset($request->yol_testi)){
                $translates = [
                    'measured_kw' => 'Ölçülen Max. Motor Gücü [kW]',
                    'measured_hp' => 'Ölçülen Max. Motor Gücü [HP]',
                    'calculated_kw' => 'Hesaplan Max. Motor Gücü [kW]',
                    'calculated_hp' => 'Hesaplan Max. Motor Gücü [HP]',
                    'calculated_rpm' => 'Hesaplan Max. Motor Gücü [RPM]',
                    'transfer_kw' => 'Aktarma Organlarındaki Kayıp Güç',
                    'transfer_hp' => 'Aktarma Organlarındaki Kayıp Güç',
                    'transfer_rpm' => 'Aktarma Organlarındaki Kayıp Güç',
                ];
                
                $inputNames = ['measured_kw', 'measured_hp', 'calculated_kw', 'calculated_hp', 'calculated_rpm', 'transfer_kw', 'transfer_hp', 'transfer_rpm'];
                
                foreach ($inputNames as $inputName) {
                    if (empty($request->input($inputName))) {
                        $errors[] = ucfirst($translates[$inputName]) . ' alanı eksik.';
                    }
                }
            }

            // Kontrol ve geri dönüş
            if (count($errors) < 1 || $expertise->ftp_ok == 1) {
                $komponent_kontrol = 1;
            } else {
                $komponent_kontrol = 0;
            }
            
            try {
                $expertise->komponent_kontrol = $komponent_kontrol;
                $expertise->save();

                if (!empty($errors)) {
                    throw new \Exception('Eksik alanlar: ' . implode('<br>', $errors));
                }
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'return_url' => '',
                    'type' => 'error'
                ]);
            }
        } elseif ($request->type == 'co2') {
            $expertise_co2 = ExpertiseCo2::where('expertise_id',$expertise->id)->first();
            if(empty($expertise_co2)){
                $expertise_co2 = new ExpertiseCo2();
                $expertise_co2->expertise_id = $expertise->id;
                $expertise_co2->status = 1;
            }
            if(!empty($request->co2_kacak_testi[0])){
                $expertise_co2->key = $request->co2_kacak_testi[0];
                $expertise_co2->note = $request->co2_kacak_testi[1];
                $expertise_co2->save();
            }else{
                return response()->json(['success'=>'false']);
            }

            logRecord(
                "update_expertise",
                $user->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydının CO2 Testi kontrolünü üstlendi.",
                $expertise->id
            );

            if (!$user->isAdmin()) {
                $expertise->komponent_kontrol_user = $user->id;
            }

            $expertise->co2_kontrol = 1;

            $expertise->save();
        }
//        if ($user->user_role_group_id == 33 || $user->user_role_group_id == 34){
//            $return_url = route('ekspertizIslemleri');
//        }
//        if (in_array($request->type,['car','brake','tire_and_rim','diagnostic','co2']))
            $return_url = route('ekspertizIslemleri');

        return response()->json(['success'=>'true','return_url'=>$return_url,'type'=>'success']);
    }

    public function aliciSozlesme($id){
        $authUser = \auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['list_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $expertise = Expertise::search($id)->where('uuid',$id)->first();

        if ($expertise){
            $stoklar = [];
            foreach ($expertise->getStocks as $stok){
                $stoklar[] = [
                    'ad' => $stok->getStock->ad,
                ];
            }

            $alici = $expertise->getAlici;
            $item = [
                'belge_no' => $expertise->belge_no,
                'belge_tarihi' => $expertise->belge_tarihi,
                'plaka' => $expertise->getCar?->plaka,
                'sase_no' => $expertise->sase_no ? $expertise->sase_no : $expertise->getCar?->sase_no,
                'getMarka' => $expertise->getCar?->getMarka?->name,
                'getModel' => $expertise->getCar?->getModel?->name,
                'km' => $expertise->getCar?->km,
                'model_yili' => $expertise->getCar?->model_yili,
                'getAlici' => $expertise->payment_type == 'sozlesme' && $expertise->getPayment->getContractCode?->getContract->customer_id != $alici->id ? $expertise->getPayment->getContractCode?->getContract?->getCustomer?->fullName . '(' . $alici?->fullName . ')' : $alici?->fullName,
                'getAliciType' => $expertise->getAlici?->type,
                'getSatici' => $expertise->getSatici?->fullName,
                'getSaticiType' => $expertise->getSatici?->type,
                'fullAddressAlici' => $expertise->getAlici?->fullAddress,
                'fullAddressSatici' => $expertise->getSatici?->fullAddress,
                'telefonAlici' => $expertise->getAlici?->telephone ?? $expertise->getAlici?->cep,
                'telefonSatici' => $expertise->getSatici?->telephone ?? $expertise->getSatici?->cep,
                'getStocks' => $stoklar,
                'uuid'=>$expertise->uuid,
            ];
            Debugbar::info($item);
        }elseif (!env('APP_LOCAL')){
            $expertise = DB::connection('mysql2')->table('T400_SRVBASLIK')->where('T400_UQ',$id)->first();
            if (!$expertise)
                return back()->with('error','Kayıt Bulunamadı!');

            $car = DB::connection('mysql2')->table('T202_ARACLAR')->where('T202_UQ',$expertise->T400_arac_UQ)->first();
            $model = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')->where('T107_kod',$car->T202_aracgrupkodu ?? 0)->first();
            if ($model)
                $marka = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')->where('T107_kod','LIKE',substr($model->T107_kod,0,5)."%")->where('T107_gruptipi',5)->first();
            else
                $marka = null;

            $stok = DB::connection('mysql2')->table('T203_STOKLAR')->where('T203_UQ',$expertise->T400_hizmet_UQ)->select('T203_stokadi')->first();
            $stocks = [['ad'=>$stok->T203_stokadi]];

            $alici = DB::connection('mysql2')->table('T201_CARILER')->where('hesap_UQ',$expertise->T400_musteri_UQ)->select('unvan','adi','soyadi','mahalle','cadde','sokak','semt','telefon','cep')->first();
            $cari = DB::connection('mysql2')->table('T201_CARILER')->where('hesap_UQ',$expertise->T400_satici_UQ)->select('unvan','adi','soyadi','mahalle','cadde','sokak','semt','telefon','cep')->first();

            $item = [
                'belge_no' => $expertise->T400_belgeno,
                'belge_tarihi' => $expertise->T400_belgetarihi,
                'plaka' => $car->T202_plakano,
                'sase_no' => $car->T202_saseno,
                'getMarka' => $marka?->T107_aciklama,
                'getModel' => $model?->T107_aciklama,
                'km' => $expertise->T400_arackm,
                'model_yili' =>  $car->T202_modelyili,
                'getAlici' => $alici ? $alici->unvan ?? ($alici->adi . ' ' . $alici->soyadi ?? '') : '',
                'getAliciType' => 'bireysel',
                'getSaticiType' => 'bireysel',
                'getSatici' => $cari ? $cari->unvan ?? ($cari->adi . ' ' . $cari->soyadi ?? '') : '',
                'fullAddressAlici' => $alici->mahalle . ' ' . $alici->cadde . ' ' . $alici->sokak . ' ' . $alici->semt,
                'fullAddressSatici' => $cari->mahalle . ' ' . $cari->cadde . ' ' . $cari->sokak . ' ' . $cari->semt,
                'telefonAlici' => $alici->telefon ?? $alici->cep,
                'telefonSatici' => $cari->telefon ?? $cari->cep,
                'getStocks' => $stocks,
                'uuid'=>$expertise->T400_UQ,
            ];
        }else
            return back()->with('error','Kayıt Bulunamadı!');


        return view('pages.expertise.alici_sozlesme',['expertise'=>$item]);
    }

    public function ekspertizRaporuContent($id, $query, $expertiseStocks, $type = 'normal')
    {
        $authUser = auth()->user();
        $bodyworks1 = array_slice(__('arrays.bodyworks'), 0, 8);
        $bodyworks2 = array_slice(__('arrays.bodyworks'), 8, 8);
        $bodyworks3 = array_slice(__('arrays.bodyworks'), 16,16);

        $hasCar = 0;
        $hasBodywork = 0;
        $hasBrake = 0;
        $hasSubControls = 0;
        $hasInternalControls = 0;
        $hasTireAndRim = 0;
        $hasDiagnostic = 0;
        $hasComponent = 0;

        $hasar_stok = 2;
        $only_co_kacak = 2;
        $stoklar = [];

        foreach ($expertiseStocks as $stok){
            $getStock = $stok->getStock;
            if($getStock->id == 215){
                $hasar_stok = 1;
            }
            if($getStock->id == 209){
                $only_co_kacak = 1;
            }

            $hasCar = $getStock->car;
            $hasBodywork = $getStock->bodywork;
            $hasBrake = $getStock->brake;
            $hasSubControls = $getStock->sub_control_and_engine;
            $hasInternalControls = $getStock->internal_control;
            $hasTireAndRim = $getStock->tire_and_rim;
            $hasDiagnostic = $getStock->diagnostic;
            $hasComponent = $getStock->component;

            $stoklar[] = [
                'id' => $getStock->id,
                'campaign_id' => $stok->campaign_id,
                'campaign_name' => $stok->getCampaign->name ?? '',
                'ad' => $getStock->ad,
            ];
        }
        if($type == 'normal' && $authUser && $hasar_stok == 1){
            return redirect()->route('hasarRaporu', $id);
        }

        $allNotes = array(
            '1' => !empty($query->getBodyworkNotes) ? $query->getBodyworkNotes->pluck('note')->toArray():array(),
            '2' => !empty($query->getBrake->getNotes) ? $query->getBrake->getNotes->pluck('note')->toArray() : array(),
            '3' => !empty($query->getDiagnosticNotes) ? $query->getDiagnosticNotes->pluck('note')->toArray() : array(),
            '4' => !empty($query->getInternalControlsNotes) ? $query->getInternalControlsNotes->pluck('note')->toArray() : array(),
            '5' => !empty($query->getTireAndRimNotes) ? $query->getTireAndRimNotes->pluck('note')->toArray() : array(),
            '6' => !empty($query->getSubControlsAndEngineNotes) ? $query->getSubControlsAndEngineNotes->pluck('note')->toArray() : array(),
            '7' => !empty($query->getComponentNotes) ? $query->getComponentNotes->pluck('note')->toArray() : array(),
        );
        $kvkk_durum = "false";
        if($authUser && $authUser->type == 'admin'){
            $kvkk_durum = "false";
        }
        elseif($authUser && !in_array($query->branch_id,$authUser->getBranchIds())){
            $kvkk_durum = "true";

        }else if($query->isCompleteDate()){
            $kvkk_durum = "true";

        }

        $is_it_over = false;
        if(!$query->isComplete()){
            if($query->ftp_ok == 2){
                $is_it_over=true;
            }
        }
        $link = route('customer.ekspertizRaporu',[$id]);



        if (env('APP_LOCAL')){
            $qr_code = "";
        }else{
            $qr_code = QrCode::format('png')
                ->size(120)
                ->generate($link);
        }
        $qr_code = "<img width='74' height='74' src='data:image/png;base64, ".base64_encode($qr_code)."'>";

        $brake = ExpertiseBrake::where('expertise_id',$query->id)->first();

        //burası da değişecek
        $co2 = ExpertiseCo2::where('expertise_id',$query->id)->first();
        if ($co2 || $only_co_kacak == 1){
            if (!empty($co2->key) && $co2->key == 'sorunlu')
                $co2Sonuc = 'Sorunlu';
            elseif(!empty($co2->key) && $co2->key == "sorunsuz")
                $co2Sonuc = 'Sorunsuz';
            else
                $co2Sonuc = "Yok";


            if (!empty($co2->note))
                $co2not = $co2->note;
            else
                $co2not = "Yok";
        }else{
            $co2Sonuc = 'Yok';
            $co2not = "Yok";
        }
        $co2Note = ExpertiseCo2Note::where('expertise_id',$query->id)->get();
        $branchTse = BranchTse::where('branch_id',$query->branch_id)->where('status',1)->whereDate('gecerlilik_tarihi','>=',now()->format('Y-m-d'))->first();
        $coordinates = BodyworkCoordinate::where('expertise_id',$query->id)->first() ?? [];
        $downloadCount = ExpertiseReportDownload::where('e_uuid',$query->uuid)->count();
        $branch = $query->getBranch;
        $car = $query->getCar;
        $alici = $query->getAlici;
        $satici = $query->getSatici;
        $expertise = [
            'processes_done' => $query->isProcessesDone(),
            'is_old' => false,
            'download_count' => $downloadCount + 1,
            'coordinates' => $coordinates,
            'tse_kodu' => $branchTse?->tse_kodu,
            'employee_downloaded' => $query->employee_downloaded,
            'km' => $query->km,
            'km_type' => $query->km_type,
            'getBranch' => $branch?->unvan,
            'bayiVergiDairesi' => $branch?->vergi_dairesi,
            'bayiVergiNo' => $branch?->vergi_no,
            'bayiAdres' => $branch?->fullAddress,
            'bayiMersis' => $branch?->mersis_no,
            'getMarka' => $car?->getMarka?->name,
            'getModel' => $car?->getModel?->name,
            'belge_no' => $query->belge_no,
            'model_yili' => $car?->model_yili,
            'created_at'=>$query->created_at->format('d.m.Y H:i:s'),
            'cikis_tarihi'=>!empty($query->cikis_tarihi) ? date('d.m.Y H:i',strtotime($query->cikis_tarihi)):'',
            'sase_no' => $query->sase_no ? $query->sase_no : $car?->sase_no,
            'getFuel' => $car->getFuel->name ??  '',
            'getGear' => $car->getGear->name ?? '',
            'motor_hacmi' => $car->motor_hacmi ?? '',
            'plaka' => $car?->plaka,
            'getBodyworks' => $this->expertiseBodyworkService->getExpertControls($query),
            'bodywork_image' => 'https://umram.online/storage/'.($query->bodywork_image ? $query->bodywork_image : ($car && $car->getCaseType ? $car->getCaseType->image : \App\Models\CarCaseType::where('status',1)->first()->image)),
            'bodywork_expert' => $this->expertiseService->getBodyworkExpert($query),
            'mechanic_experts' => $this->expertiseService->getMechanicExperts($query),
            'service_manager' => $this->expertiseService->getServiceManager($query),
            'creator_expert' => $this->expertiseService->getCreatorExpert($query),
            'payment_type' => $this->expertiseService->getPaymentTypeText($query),
            'getAlici' => $query->payment_type == 'sozlesme' && $query->getPayment?->getContractCode?->getContract->customer_id != $alici->id ? $query->getPayment?->getContractCode?->getContract?->getCustomer?->fullName . '(' . $alici?->fullName . ')' : $alici?->fullName,
            'getAliciType' => $alici?->type,
            'getAliciTel' => $alici?->phone(),




            'getAliciTc' => $alici?->tc_no ?? '',
            'getAliciVergiNo' => $alici?->vergi_no ?? '',
            'getAliciEsbis' => $alici?->esbis_no ?? '',
            'getAliciMersis' => $alici?->mersis ?? '',
            'getAliciYetkiBelgeNo' => $alici?->yetki_belge_no ?? '',




            'getSatici' => $satici?->fullName,
            'getSaticiType' => $satici?->type,
            'getSaticiTel' => $satici?->phone(),
            'getSaticiTc' => $satici?->tc_no ?? '',
            'getSaticiVergiNo' => $satici?->vergi_no ?? '',
            'getSaticiEsbis' => $satici?->esbis_no ?? '',
            'getSaticiMersis' => $satici?->mersis ?? '',
            'getSaticiYetkiBelgeNo' => $satici?->yetki_belge_no ?? '',
            'fullAddressAlici' => $alici?->fullAddress,
            'fullAddressSatici' => $satici?->fullAddress,
            'getSubControlsAndEngines' => $this->expertiseSubControlAndEngineService->getExpertControls($query),
            'getInternalControls' => $this->expertiseInternalControlService->getExpertControls($query),
            'getTireAndRims' => $this->expertiseTireAndRimService->getExpertControls($query),
            'getDiagnostics' => $query->getDiagnostics,
            'getComponents' => $this->expertiseComponentService->getExpertControls($query),
            'allNotes' => $allNotes,
            'uuid'=>$query->uuid,
            'kvkk_durum' =>$kvkk_durum,
            'is_it_over' => $is_it_over,
            'status' => $query->status,
            'qr'=>$qr_code,
            'co2_kacak_testi' => (!empty($query->getStockhasOne) &&  ($query->getStockhasOne?->getStock?->co2 == 1 || $query->getStockhasOne->campaign_id == 261)) ? true:false,
            'on_yanal_kayma' => $brake?->yanal_kayma_on,
            'arka_yanal_kayma' => $brake?->yanal_kayma_arka,
            'on_fren_dengesizlik_orani' => (float) $brake?->dengesizlik_orani_on,
            'arka_fren_dengesizlik_orani' => (float) $brake?->dengesizlik_orani_arka,
            'fren_max_a' => (float) replaceFloat($brake?->max_kuvvet_on_1),
            'fren_max_b' => (float) replaceFloat($brake?->max_kuvvet_arka_1),
            'fren_max_c' => (float) replaceFloat($brake?->max_kuvvet_on_2),
            'fren_max_d' => (float) replaceFloat($brake?->max_kuvvet_arka_2),
            'fren_on_bosta_surtunme_a' => (float) replaceFloat($brake?->on_dingil_bosta_a),
            'fren_on_bosta_surtunme_b' => (float) replaceFloat($brake?->on_dingil_bosta_b),
            'fren_on_yalpa_a' => (float) replaceFloat($brake?->yalpa_orani_on_1),
            'fren_on_yalpa_b' => (float) replaceFloat($brake?->yalpa_orani_on_2),
            'fren_arka_bosta_surtunme_a' => (float) replaceFloat($brake?->arka_dingil_bosta_a),
            'fren_arka_bosta_surtunme_b' => (float) replaceFloat($brake?->arka_dingil_bosta_b),
            'fren_arka_yalpa_a' => (float) replaceFloat($brake?->yalpa_orani_arka_1),
            'fren_arka_yalpa_b' => (float) replaceFloat($brake?->yalpa_orani_arka_2),
            'fren_el_freni_a' => (float) replaceFloat($brake?->el_freni_kuvvet_a),
            'fren_el_freni_b' => (float) replaceFloat($brake?->el_freni_kuvvet_b),
            'fren_el_freni_dengesizlik' => (int) $brake?->el_freni_dengesizlik_orani,
            'suspansiyon_min_tutunma_a' => $brake?->getSuspensionFrontLeft(),
            'suspansiyon_min_tutunma_b' => $brake?->getSuspensionRearLeft(),
            'suspansiyon_min_tutunma_c' => $brake?->getSuspensionFrontRight(),
            'suspansiyon_min_tutunma_d' => $brake?->getSuspensionRearRight(),
            'getStocks' => $stoklar,
            'co2Sonuc' => $co2Sonuc,
            'co2not' => $co2not,
            'co2' => $co2,
            'only_co_kacak'=>$only_co_kacak,
            'audio_url' => $query->audio_url,
            'co2Note' => $co2Note,
            'hasCar' => $hasCar,
            'hasBrake' => $hasBrake,
            'hasBodywork' => $hasBodywork,
            'hasInternalControls' => $hasInternalControls,
            'hasSubControls' => $hasSubControls,
            'hasDiagnostic' => $hasDiagnostic,
            'hasComponent' => $hasComponent,
            'hasTireAndRim' => $hasTireAndRim,
        ];

        return [
            'expertise' => $expertise,
            'bodyworks1' => $bodyworks1,
            'bodyworks2' => $bodyworks2,
            'bodyworks3' => $bodyworks3,
            'engines1' => $this->expertiseSubControlAndEngineService->getEngineControls($car),
            'engines2' => $this->expertiseSubControlAndEngineService->getSubControls($car),
        ];
    }

    public function ekspertizRaporu($id, Request $request){
        /** @var \App\Models\User */
        $authUser = \auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['list_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');
        $query = Expertise::where('uuid',$id)->first();

        if ($query){
            $expertiseStocks = ExpertiseStock::where('expertise_id',$query->id)->get();
            $hasar_stok = 2;

            foreach ($expertiseStocks as $stok){
                $getStock = $stok->getStock;
                if($getStock->id == 215){
                    $hasar_stok = 1;
                }
            }
            if($authUser && $hasar_stok == 1){
                return redirect()->route('hasarRaporu', $id);
            }

            // We will mask experts, if the user is not admin
            if (!$authUser->isAdmin()) {
                $this->expertiseService->setMask(true);
            }

            $data = $this->ekspertizRaporuContent($id,$query,$expertiseStocks);
            $expertise = $data['expertise'];
            $bodyworks1 = $data['bodyworks1'];
            $bodyworks2 = $data['bodyworks2'];
            $bodyworks3 = $data['bodyworks3'];
            $engines1 = $data['engines1'];
            $engines2 = $data['engines2'];

            return view('pages.expertise.ekspertiz_raporu',compact([
                'expertise','bodyworks1','bodyworks2','bodyworks3','engines1','engines2']));
        }
        elseif (!env('APP_LOCAL')){
            $bodyworks1 = array_slice(__('arrays.bodyworks'), 0, 8);
            $bodyworks2 = array_slice(__('arrays.bodyworks'), 8, 8);
            $bodyworks3 = array_slice(__('arrays.bodyworks'), 16,16);

            $engines1 = array_slice(__('arrays.sub_controls_and_engines'), 0, 10);
            $engines2 = array_slice(__('arrays.sub_controls_and_engines'), 10, 10);

            $oldDB = T400SRVBASLIK::search($id)->where('T400_UQ',$id)->first();
            if (!$oldDB)
            {
                $oldDB = DB::connection('mysql2')->table('T400_SRVBASLIK')->where('T400_UQ',$id)->first();
                if(!$oldDB){
                    return back()->with('error','Kayıt Bulunamadı!');
                }
            }


            $branch = DB::connection('mysql2')->table('T103_SUBELER')->where('T103_kod',$oldDB->T400_subekodu)->first();
            $newBranch = Branch::where('belge_kod',$branch?->T103_kod)->first();
            $car = DB::connection('mysql2')->table('T202_ARACLAR')->where('T202_UQ',$oldDB->T400_arac_UQ)->first();
            $model = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')->where('T107_kod',$car->T202_aracgrupkodu ?? 0)->first();
            if ($model)
                $marka = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')->where('T107_kod','LIKE',substr($model->T107_kod,0,5)."%")->where('T107_gruptipi',5)->first();
            else
                $marka = null;
            $yakitTipi = !empty($car->T202_motortipikodu) ? DB::connection('mysql2')->table('T108_MOTORTIPLERI')->where('T108_kod',$car->T202_motortipikodu)->first() : '';
            $vitesTipi = !empty($car->T202_vitestipikodu) ?  DB::connection('mysql2')->table('T109_VITESTIPLERI')->where('T109_kod',$car->T202_vitestipikodu)->first() : '';
            $alici = !empty($oldDB->T400_musteri_UQ) ? DB::connection('mysql2')->table('T201_CARILER')->where('hesap_UQ',$oldDB->T400_musteri_UQ)->select('unvan','adi','soyadi','mahalle','cadde','sokak','semt','telefon','cep')->first() : '';
            $cari = !empty($oldDB->T400_satici_UQ) ?  DB::connection('mysql2')->table('T201_CARILER')->where('hesap_UQ',$oldDB->T400_satici_UQ)->select('unvan','adi','soyadi','mahalle','cadde','sokak','semt','telefon','cep')->first() : '';
            $stok = DB::connection('mysql2')->table('T203_STOKLAR')->where('T203_UQ',$oldDB->T400_hizmet_UQ)->select('T203_stokadi','T203_UQ')->first();
            $localeStock = Stock::where('kod',$stok->T203_UQ)->select('id')->first();
            $stocks = [['ad'=>$stok->T203_stokadi,'id'=>$localeStock->id ?? 0]];

            $yanalKayma = DB::connection('mysql2')->table('T410_YANALKAYMA')->where('T410_servis_UQ',$id)->first();
            $fren = DB::connection('mysql2')->table('T420_FREN')->where('T420_servis_UQ',$id)->first();
            $suspansiyon = DB::connection('mysql2')->table('T430_SUSPANSIYON')->where('T430_servis_UQ',$id)->first();



            $getBodyworks = [];
            $resim = DB::connection('mysql2')->table('T403_SRVRESIM')->where('T403_servis_UQ',$id)->first();
            foreach (__('arrays.bodyworks') as $key => $bodywork){
                $getBodyworkKod = DB::connection('mysql2')->table('T116_TESTALANLARI')->where('T116_aciklama','LIKE',"%{$bodywork}%")->first();
                $getBodywork = DB::connection('mysql2')->table('T440_KAPORTA')->where('T440_servis_UQ',$id)->where('T440_alan_kodu',$getBodyworkKod->T116_kod)->first();
                $getBodyworks[$key] = [
                    'orijinal' => $getBodywork?->T440_orjinal ?? 0,
                    'boyali' => $getBodywork?->T440_boya ?? 0,
                    'degisen' => $getBodywork?->T440_degisen ?? 0,
                    'duz' => $getBodywork?->T440_duz ?? 0,
                    'note' => $getBodywork?->T440_aciklama ?? '',
                    'title'=>$bodywork
                ];
            }
            $not_answer_description = DB::connection('mysql2')
                ->table('T140_TESTSONUCKOD')
                ->select('T140_kod as key', 'T140_aciklama as note')
                ->pluck('note', 'key')
                ->toArray();
            $getSubControlsAndEngines = [];

            foreach (__('arrays.sub_controls_and_engines') as $key => $subControlEngine){
                $getSubControlAndEngineKod = DB::connection('mysql2')->table('T116_TESTALANLARI')->where('T116_aciklama','LIKE',"%{$subControlEngine}%")->where('T116_test_kodu',8)->whereIn('T116_grupno',[1,3])->first();
                $getSubControlAndEngine = DB::connection('mysql2')->table('T451_MEKANIKTSE')->where('T451_servis_UQ',$id)->where('T451_alan_kodu',$getSubControlAndEngineKod->T116_kod ?? 0)->first();

                $getSubControlsAndEngines[$key] = [
                    'answer' => !empty($getSubControlAndEngine->T451_sonuc_kodu) ? $not_answer_description[$getSubControlAndEngine->T451_sonuc_kodu] : '',
                    'note' => $getSubControlAndEngine?->T451_aciklama ?? '',
                    'title'=>$subControlEngine
                ];
            }

            $getInternalControls = [];
            foreach (__('arrays.internal_controls') as $key => $internalControl){
                $getInternalControlKod = DB::connection('mysql2')->table('T116_TESTALANLARI')->where('T116_aciklama','LIKE',"%{$internalControl}%")->where('T116_grupno',2)->first();
                $getInternalControl = DB::connection('mysql2')->table('T451_MEKANIKTSE')->where('T451_servis_UQ',$id)->where('T451_alan_kodu',$getInternalControlKod->T116_kod ?? 0)->first();
                $getInternalControls[$key] = [
                    'answer' => !empty($getInternalControl->T451_sonuc_kodu) ?  $not_answer_description[$getInternalControl->T451_sonuc_kodu] : '',
                    'sorunlu_mu' => $getInternalControl?->T451_sonuc_kodu == 1050 ? 1 : 0,
                    'note' => $getInternalControl?->T451_aciklama ?? '',
                    'title'=>$internalControl
                ];
            }

            $getTireAndRims = [];
            foreach (__('arrays.tire_and_rims') as $key => $tireAndRim){
                $getTireAndRimKod = DB::connection('mysql2')->table('T116_TESTALANLARI')->where('T116_aciklama','LIKE',"%{$tireAndRim}%")->where('T116_grupno',4)->first();
                $getTireAndRim = DB::connection('mysql2')->table('T451_MEKANIKTSE')->where('T451_servis_UQ',$id)->where('T451_alan_kodu',$getTireAndRimKod->T116_kod ?? 0)->first();
                $getTireAndRims[$key] = [
                    'answer' => !empty($getInternalControl->T451_sonuc_kodu) ? $not_answer_description[$getInternalControl->T451_sonuc_kodu] : '',
                    'sorunlu_mu' => $getTireAndRim?->T451_sonuc_kodu == 1050 ? 1 : 0,
                    'note' => $getTireAndRim?->T451_aciklama ?? '',
                    'dis' => $getTireAndRim?->T451_deger_1 ?? '',
                    'basinc' => $getTireAndRim?->T451_deger_2 ?? '',
                    'title'=>$tireAndRim
                ];
            }


            $getComponents = [];
            foreach (__('arrays.components') as $key => $component){
                $getComponentKod = DB::connection('mysql2')->table('T116_TESTALANLARI')->where('T116_aciklama',$component)->where('T116_test_kodu',8)->whereIn('T116_grupno',[3,6])->first();
                $getComponent = DB::connection('mysql2')->table('T451_MEKANIKTSE')->where('T451_servis_UQ',$id)->where('T451_alan_kodu',$getComponentKod->T116_kod ?? 0)->first();
                $getComponents[$key] = [
                    'answer' =>!empty($getComponent->T451_sonuc_kodu) ? strtolower($not_answer_description[$getComponent->T451_sonuc_kodu]):'',
                    'note' => $getInternalcontrol?->note ?? '',
                    'title'=>$component
                ];
            }

            $brakenot = OldExpertiseNote::search($id)->where('T402_test_kodu',2)->get()->pluck('T402_aciklama')->toArray();
            $bodyworknot = OldExpertiseNote::search($id)->where('T402_test_kodu',4)->get()->pluck('T402_aciklama')->toArray();
            $diagnosticNote = OldExpertiseNote::search($id)->where('T402_test_kodu',6)->get()->pluck('T402_aciklama')->toArray();
            $internalControlNote = OldExpertiseNote::search($id)->where('T402_test_kodu',8)->get()->pluck('T402_aciklama')->toArray();
            $tireAndRimNote = OldExpertiseNote::search($id)->where('T402_test_kodu',10)->get()->pluck('T402_aciklama')->toArray();
            $subControlAndEngineNote = OldExpertiseNote::search($id)
                ->query(function ($query) {
                    $query->whereIn('T402_test_kodu', [5, 9]);
                })
                ->get()->pluck('T402_aciklama')->toArray();
            $componentNote = OldExpertiseNote::search($id)->where('T402_test_kodu',11)->get()->pluck('T402_aciklama')->toArray();
            /*indeksleme bitince kalkacak alan*/
            if(empty($brakenot)){
                $brakenot = DB::connection('mysql2')
                    ->table('T402_SRVNOTLARI')
                    ->where('T402_servis_UQ', $id)
                    ->where('T402_test_kodu', 6)
                    ->pluck('T402_aciklama')->toArray();
            }
            if(empty($bodyworknot)){
                $bodyworknot = DB::connection('mysql2')
                    ->table('T402_SRVNOTLARI')
                    ->where('T402_servis_UQ', $id)
                    ->where('T402_test_kodu', 4)
                    ->pluck('T402_aciklama')->toArray();
            }
            if(empty($diagnosticNote)){
                $diagnosticNote = DB::connection('mysql2')
                    ->table('T402_SRVNOTLARI')
                    ->where('T402_servis_UQ', $id)
                    ->where('T402_test_kodu', 6)
                    ->pluck('T402_aciklama')->toArray();
            }
            if(empty($internalControlNote)){
                $internalControlNote = DB::connection('mysql2')
                    ->table('T402_SRVNOTLARI')
                    ->where('T402_servis_UQ', $id)
                    ->where('T402_test_kodu', 8)
                    ->pluck('T402_aciklama')->toArray();
            }
            if(empty($tireAndRimNote)){
                $tireAndRimNote = DB::connection('mysql2')
                    ->table('T402_SRVNOTLARI')
                    ->where('T402_servis_UQ', $id)
                    ->where('T402_test_kodu', 10)
                    ->pluck('T402_aciklama')->toArray();
            }
            if(empty($subControlAndEngineNote)){
                $subControlAndEngineNote = DB::connection('mysql2')
                    ->table('T402_SRVNOTLARI')
                    ->where('T402_servis_UQ', $id)
                    ->whereIn('T402_test_kodu', [5,9])
                    ->pluck('T402_aciklama')->toArray();
            }
            /*indeksleme bitince kalkacak alan*/

            $allNotes = array(
                '1' => $bodyworknot,
                '2' => $brakenot,
                '3' => $diagnosticNote,
                '4' => $internalControlNote,
                '5' => $tireAndRimNote,
                '6' => $subControlAndEngineNote,
                '7' => $componentNote,
            );
            $link = route('customer.ekspertizRaporu',[$id]);


            $qr_code = QrCode::format('png')
                ->size(120)
                ->generate($link);
            $qr_code = "<img src='data:image/png;base64, ".base64_encode($qr_code)."'>";
            $downloadCount = ExpertiseReportDownload::where('e_uuid',$id)->count();
            $expertise = [
                'processes_done' => true,
                'is_old' => true,
                'download_count' => $downloadCount + 1,
                'tse_kodu' => $branch?->T103_sube_tse_kodu,
                'coordinates' => [],
                'employee_downloaded' => 1,
                'km' => $oldDB->T400_arackm,
                'km_type' => 1,
                'getBranch' => $branch->T103_kisaadi ?? '',
                'bayiVergiDairesi' => $branch->T103_vergidairesi,
                'bayiVergiNo' => $branch?->T103_vergino ??  $branch?->T103_tckimlikno,
                'bayiAdres' => $branch?->T103_mahalle . ' ' . $branch?->T103_cadde . ' ' . $branch?->T103_sokak . ' ' . $branch?->T103_semt . ' ' . $branch?->T103_ilce . ' ' . $branch?->T103_il,
                'bayiMersis' => $newBranch?->mersis_no,
                'getMarka' => $marka->T107_aciklama ?? '',
                'getModel' => $model->T107_aciklama ?? '',
                'belge_no' => $oldDB->T400_belgeno ?? '',
                'model_yili' => $car->T202_modelyili ?? '',
                'created_at'=>!empty($oldDB->T400_belgetarihi) ? date('d.m.Y H:i:s',strtotime($oldDB->T400_belgetarihi)) : '',
                'cikis_tarihi'=>!empty($oldDB->T400_cikistarihi) ? date('d.m.Y H:i:s',strtotime($oldDB->T400_cikistarihi)) : '',
                'sase_no' => $car->T202_saseno ?? '',
                'motor_hacmi' => $car->T202_motor_hacmi ?? '',
                'getFuel' => $yakitTipi?->T108_aciklama ?? '',
                'getGear' => $vitesTipi?->T109_aciklama ?? '',
                'plaka' => $car->T202_plakano ?? '',
                'getBodyworks' => $getBodyworks,
                'bodywork_image' => 'data:image/jpeg;base64,'.base64_encode($resim?->T403_resim),
                'bodywork_expert' => '',
                'mechanic_experts' => '',
                'service_manager' => '',
                'creator_expert' => '',
                'payment_type' => '',
                'getAlici' => $alici ? $alici->unvan ?? ($alici->adi . ' ' . $alici->soyadi ?? '') : '',
                'getAliciType' => 'bireysel',
                'getAliciTel' => $alici ? $alici->cep ?? $alici->telefon : '',
                'getAliciTc' => $query->getAlici?->tc_no ?? '',
                'getAliciVergiNo' => $query->getAlici?->vergi_no ?? '',
                'getAliciEsbis' => $query->getAlici?->esbis_no ?? '',
                'getAliciMersis' => $query->getAlici?->mersis ?? '',
                'getAliciYetkiBelgeNo' => $query->getAlici?->yetki_belge_no ?? '',

                'getSaticiTc' => $query->getSatici?->tc_no ?? '',
                'getSaticiVergiNo' => $query->getSatici?->vergi_no ?? '',
                'getSaticiEsbis' => $query->getSatici?->esbis_no ?? '',
                'getSaticiMersis' => $query->getSatici?->mersis ?? '',
                'getSaticiYetkiBelgeNo' => $query->getSatici?->yetki_belge_no ?? '',

                'getSatici' => $cari ? $cari->unvan ?? ($cari->adi . ' ' . $cari->soyadi ?? '') : '',
                'getSaticiType' => 'bireysel',
                'getSaticiTel' => $cari ? $cari->cep ?? $cari->telefon : '',
                'fullAddressAlici' => $alici ? $alici->mahalle . ' ' . $alici->cadde . ' ' . $alici->sokak . ' ' .$alici->semt . ' '  : '',
                'fullAddressSatici' => $cari ? $cari->mahalle . ' ' . $cari->cadde . ' ' . $cari->sokak . ' ' .$cari->semt . ' '  : '',
                'getSubControlsAndEngines' => $getSubControlsAndEngines,
                'getInternalControls' => $getInternalControls,
                'getTireAndRims' => $getTireAndRims,
                'getComponents' => $getComponents,
                'uuid'=>$oldDB->T400_UQ ?? '',
                'kvkk_durum' => 'true',
                'allNotes' => $allNotes, // ESKİ VERİTABANINDAN NOTLAR ALANINI TOPLU SEKİLDE BURAYA GÖNDERİLMESİ GEREKİYOR
                'is_it_over' => false,
                'status' => 1,
                'qr'=>$qr_code,
                'co2_kacak_testi' => false,
                'on_yanal_kayma' => !is_null($yanalKayma) ? $yanalKayma?->T410_onkayma : '',
                'arka_yanal_kayma' => !is_null($yanalKayma) ? $yanalKayma?->T410_arkakayma : '',
                'on_fren_dengesizlik_orani' => !is_null($fren) ? (float) $fren?->T420_onfrendngszOran : '',
                'arka_fren_dengesizlik_orani' => !is_null($fren)? (float) $fren?->T420_arkafrendngszOran : '',
                'fren_max_a' => !is_null($fren)? (float) $fren?->T420_maksimumA : '',
                'fren_max_b' => !is_null($fren)? (float) $fren?->T420_maksimumB : '',
                'fren_max_c' => !is_null($fren)? (float) $fren?->T420_maksimumC : '',
                'fren_max_d' => !is_null($fren)? (float) $fren?->T420_maksimumD : '',
                'fren_on_bosta_surtunme_a' => !is_null($fren)? (float) $fren?->T420_bostasurtunmeA : '',
                'fren_on_bosta_surtunme_b' => !is_null($fren)? (float) $fren?->T420_bostasurtunmeB : '',
                'fren_on_yalpa_a' => !is_null($fren)? (float) $fren?->T420_yalpasurtunmeA : '',
                'fren_on_yalpa_b' => !is_null($fren)? (float) $fren?->T420_yalpasurtunmeB : '',
                'fren_arka_bosta_surtunme_a' => !is_null($fren)? (float) $fren?->T420_bostasurtunmeC : '',
                'fren_arka_bosta_surtunme_b' => !is_null($fren)? (float) $fren?->T420_bostasurtunmeD : '',
                'fren_arka_yalpa_a' => !is_null($fren)? (float) $fren?->T420_yalpasurtunmeC : '',
                'fren_arka_yalpa_b' => !is_null($fren)? (float) $fren?->T420_yalpasurtunmeD : '',
                'fren_el_freni_a' => !is_null($fren)? (float) $fren?->T420_elfreniC : '',
                'fren_el_freni_b' => !is_null($fren)? (float) $fren?->T420_elfreniD : '',
                'fren_el_freni_dengesizlik' => !is_null($fren)? (int) $fren?->T420_elfrenidngszOran : '',
                'suspansiyon_min_tutunma_a' => !is_null($suspansiyon) ? (float) $suspansiyon?->T430_OranA : '',
                'suspansiyon_min_tutunma_b' => !is_null($suspansiyon) ? (float) $suspansiyon?->T430_OranB : '',
                'suspansiyon_min_tutunma_c' => !is_null($suspansiyon) ? (float) $suspansiyon?->T430_OranC : '',
                'suspansiyon_min_tutunma_d' => !is_null($suspansiyon) ? (float) $suspansiyon?->T430_OranD : '',
                'getStocks' => $stocks,
                'co2Sonuc' => 'Yok',
                'audio_url' => '',
                'only_co_kacak'=>'',
                'hasCar' => 1,
                'hasBrake' => 1,
                'hasBodywork' => 1,
                'hasInternalControls' => 1,
                'hasSubControls' => 1,
                'hasDiagnostic' => 1,
                'hasComponent' => 1,
                'hasTireAndRim' => 1,
            ];

            return view('pages.expertise.ekspertiz_raporu',compact([
                'expertise','bodyworks1','bodyworks2','bodyworks3','engines1','engines2']));

        }
        else{
            return back()->with('error','Kayıt Bulunamadı!');
        }
    }

    public function deleteNotOpenedExpertises()
    {
        $currentTime = \Carbon\Carbon::now();
        $start = \Carbon\Carbon::createFromTime(21, 0, 0);
        $end = \Carbon\Carbon::createFromTime(23, 59, 59);
        if ($currentTime->between($start, $end)){
            Expertise::where('manuel_save','!=',1)->delete();
        }

        return response()->json(['success'=>'true'],200);
    }

    /**
     * This method calls from the plesk Scheduled Tasks
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function autoExpertiseFtpOk(){
        set_time_limit(0);
        $currentTime = \Carbon\Carbon::now();
        if (isset($_GET['uuid']) || isset($_GET['yesterday']))
            $start = \Carbon\Carbon::createFromTime(0, 0, 0);
        else
            $start = \Carbon\Carbon::createFromTime(21, 0, 0);

        $end = \Carbon\Carbon::createFromTime(23, 59, 59);

        if ($currentTime->between($start, $end)) {
            Expertise::when(isset($_GET['uuid']) && $_GET['uuid'] != '',function ($query){
                return $query->where('uuid',$_GET['uuid'])->where('created_at','<=',now()->subHour());
            })
            ->where('branch_id', '!=', '160')
            ->when(!isset($_GET['uuid']) || $_GET['uuid'] == '',function ($query){
                if (!isset($_GET['yesterday']))
                    return $query->where('manuel_save',1)
                        ->where('ftp_ok','!=',1);
                else
                    return $query->where('manuel_save',1)
                        ->where('ftp_ok','!=',1)
                        ->where('created_at','<=',now()->subDay()->endOfDay());
            })
            ->orderBy('id','desc')
            ->chunk(100,function($expertises){
                    foreach ($expertises as $exp){
                        Log::debug("$exp->uuid ekspertiz kapatılıyor.");
                        if ($exp->updated_at > now()->subMinutes(10 && isset($_GET['uuid']))){
                            continue;
                        }
                        $expertiseStocks = ExpertiseStock::where('expertise_id',$exp->id)->get();

                        $data = $this->ekspertizRaporuContent($exp->id,$exp,$expertiseStocks,'auto');
                        $expertise = $data['expertise'];
                        $bodyworks1 = $data['bodyworks1'];
                        $bodyworks2 = $data['bodyworks2'];
                        $bodyworks3 = $data['bodyworks3'];
                        $engines1 = $data['engines1'];
                        $engines2 = $data['engines2'];

                            $mask = 'filtreli';
                            $maskedHtml = view('pages.expertise.rapor.content', compact([
                                'expertise', 'bodyworks1', 'bodyworks2', 'bodyworks3', 'engines1', 'engines2', 'mask'
                            ]))->render();

                            $mask = 'filtresiz';
                            $nonMaskedHtml = view('pages.expertise.rapor.content', compact([
                                'expertise', 'bodyworks1', 'bodyworks2', 'bodyworks3', 'engines1', 'engines2', 'mask'
                            ]))->render();

                            $html_data = "<meta charset='UTF-8'><div id='divRaporEkrani'>" . $nonMaskedHtml . "</div>";
                            $masked_html_data = "<meta charset='UTF-8'><div id='divRaporEkrani'>" . $maskedHtml . "</div>";

                            $file = 'expertises_ftp/' . $exp->uuid . '/non_masked/' . $exp->uuid . '.html'; // Yazılacak dosyanın adı
                            $file_masked = 'expertises_ftp/' . $exp->uuid . '/masked/' . $exp->uuid . '.html'; // Yazılacak dosyanın adı

                            if (!Storage::exists($file)) {
                                Storage::put($file, $html_data);
                            }

                            if (!Storage::exists($file_masked)) {
                                Storage::put($file_masked, $masked_html_data);
                            }




                        /*Dosyaları Kapat*/
                        $getStockhasOne = $exp->getStockhasOne;
                        if ($getStockhasOne){
                            $getStock = $getStockhasOne->getStock;
                            if($getStock->car == 1){
                                $exp->arac_kontrol = 1;
                            }
                            if($getStock->brake == 1){
                                $exp->fren_kontrol = 1;
                            }
                            if($getStock->bodywork == 1){
                                $exp->kaporta_kontrol = 1;
                            }
                            if($getStock->tire_and_rim == 1){
                                $exp->lastik_jant_kontrol = 1;
                            }
                            if($getStock->diagnostic == 1){
                                $exp->diagnostic_kontrol = 1;
                            }
                            if($getStock->internal_control == 1){
                                $exp->ic_kontrol = 1;
                            }
                            if($getStock->sub_control_and_engine == 1){
                                $exp->alt_motor_kontrol = 1;
                            }
                            if($getStock->component == 1){
                                $exp->komponent_kontrol = 1;
                            }
                            if($getStock->co2 == 1){
                                $exp->co2_kontrol = 1;
                            }


                            /*Dosyaları Kapat*/


                            $exp->status = 1;
                            $exp->ftp_ok = 1;
                            if (!isset($_GET['uuid']))
                                $exp->ftp_date = date('Y-m-d H:i:s');
                            if (is_null($exp->cikis_tarihi))
                                $exp->cikis_tarihi = date('Y-m-d H:i:s');
                            $exp->save();

                            Log::debug("$exp->uuid ekspertiz kapatıldı.");
                        }
                    }
                });
        }


        return response()->json(['success'=>'true'],200);
    }

    public function hasarRaporu($id)
    {
        $authUser = auth()->user();
        $bodyworks1 = array_slice(__('arrays.bodyworks'), 0, 8);
        $bodyworks2 = array_slice(__('arrays.bodyworks'), 8, 8);
        $bodyworks3 = array_slice(__('arrays.bodyworks'), 16,16);
        $engines1 = array_slice(__('arrays.sub_controls_and_engines'), 0, 10);
        $engines2 = array_slice(__('arrays.sub_controls_and_engines'), 10, 10);
        $query = Expertise::where('uuid',$id)->first();
        if ($query){
            $stoklar = [];
            $expertiseStocks = ExpertiseStock::where('expertise_id',$query->id)->get();
            foreach ($expertiseStocks as $stok){
                $stoklar[] = [
                    'id' => $stok->getStock->id,
                    'campaign_id' => $stok->campaign_id,
                    'ad' => $stok->getStock->ad,
                ];
            }
            $getBodyworks = [];
            $bodyworks = ExpertiseBodywork::where('expertise_id', $query->id)->get()->keyBy('key');
            foreach (__('arrays.bodyworks') as $key => $bodywork) {
                $getBodywork = $bodyworks->get($key);
                $getBodyworks[$key] = [
                    'orijinal' => $getBodywork?->orijinal ?? 0,
                    'boyali' => $getBodywork?->boyali ?? 0,
                    'degisen' => $getBodywork?->degisen ?? 0,
                    'duz' => $getBodywork?->duz ?? 0,
                    'note' => $getBodywork?->note ?? '',
                    'title' => $bodywork
                ];
            }

            $getSubControlsAndEngines = [];
            $subControlsAndEngines = ExpertiseSubControlAndEngine::where('expertise_id', $query->id)->get()->keyBy('key');
            foreach (__('arrays.sub_controls_and_engines') as $key => $subControlAndEngine) {
                $getInternalcontrol = $subControlsAndEngines->get($key);
                $getSubControlsAndEngines[$key] = [
                    'answer' => $getInternalcontrol?->answer ?? '',
                    'note' => $getInternalcontrol?->note ?? '',
                    'title' => $subControlAndEngine
                ];
            }

            $getInternalControls = [];
            $internalControls = ExpertiseInternalControl::where('expertise_id', $query->id)->get()->keyBy('key');
            foreach (__('arrays.internal_controls') as $key => $internalControl) {
                $getInternalcontrol = $internalControls->get($key);
                $getInternalControls[$key] = [
                    'answer' => $getInternalcontrol?->answer ?? '',
                    'sorunlu_mu' => $getInternalcontrol?->sorunlu_mu ?? 0,
                    'note' => $getInternalcontrol?->note ?? '',
                    'title' => $internalControl
                ];
            }

            $getTireAndRims = [];
            $tireAndRims = ExpertiseTireAndRim::where('expertise_id', $query->id)->get()->keyBy('key');
            foreach (__('arrays.tire_and_rims') as $key => $tireAndRim) {
                $getInternalcontrol = $tireAndRims->get($key);
                $getTireAndRims[$key] = [
                    'sorunlu_mu' => $getInternalcontrol?->sorunlu_mu ?? null,
                    'note' => $getInternalcontrol?->note ?? '',
                    'dis' => $getInternalcontrol?->dis ?? '',
                    'basinc' => $getInternalcontrol?->basinc ?? '',
                    'title' => $tireAndRim
                ];
            }

            $getComponents = [];
            $components = ExpertiseComponent::where('expertise_id', $query->id)->get()->keyBy('key');
            foreach (__('arrays.components') as $key => $component) {
                $getInternalcontrol = $components->get($key);
                $getComponents[$key] = [
                    'answer' => $getInternalcontrol?->answer ?? '',
                    'status' => $getInternalcontrol?->status ?? 0,
                    'note' => $getInternalcontrol?->note ?? '',
                    'title' => $component
                ];
            }

            $allNotes = array(
                '1' => !empty($query->getBodyworkNotes) ? $query->getBodyworkNotes->pluck('note')->toArray():array(),
                '2' => !empty($query->getBrake->getNotes) ? $query->getBrake->getNotes->pluck('note')->toArray() : array(),
                '3' => !empty($query->getDiagnosticNotes) ? $query->getDiagnosticNotes->pluck('note')->toArray() : array(),
                '4' => !empty($query->getInternalControlsNotes) ? $query->getInternalControlsNotes->pluck('note')->toArray() : array(),
                '5' => !empty($query->getTireAndRimNotes) ? $query->getTireAndRimNotes->pluck('note')->toArray() : array(),
                '6' => !empty($query->getSubControlsAndEngineNotes) ? $query->getSubControlsAndEngineNotes->pluck('note')->toArray() : array(),
                '7' => !empty($query->getComponentNotes) ? $query->getComponentNotes->pluck('note')->toArray() : array(),
            );
            $kvkk_durum = "false";
            if($authUser->type == 'admin'){
                $kvkk_durum = "false";
            }
            elseif(!in_array($query->branch_id,$authUser->getBranchIds())){
                $kvkk_durum = "true";

            }else if($query->isCompleteDate()){
                $kvkk_durum = "true";

            }

            $is_it_over = false;
            if(!$query->isComplete()){
                if($query->ftp_ok == 2){
                    $is_it_over=true;
                }
            }
            $link = route('customer.ekspertizRaporu',[$id]);



            if (env('APP_LOCAL')){
                $qr_code = "";
            }else{
                $qr_code = QrCode::format('png')
                    ->size(120)
                    ->generate($link);
            }
            $qr_code = "<img src='data:image/png;base64, ".base64_encode($qr_code)."'>";

            $brake = ExpertiseBrake::where('expertise_id',$query->id)->first();

            $co2 = ExpertiseSubControlAndEngine::where('expertise_id',$query->id)->where('key','co2_kacak_testi')->first();
            if ($co2){
                if ($co2->answer == 'sorunlu')
                    $co2Sonuc = 'Sorunlu';
                else
                    $co2Sonuc = 'Sorunsuz';
            }else{
                $co2Sonuc = 'Yok';
            }

            $settings = Setting::first();
            $alici = $query->getAlici;

            $expertise = [
                'processes_done' => $query->isProcessesDone(),
                'is_old' => false,
                'employee_downloaded' => $query->employee_downloaded,
                'km' => $query->km,
                'km_type' => $query->km_type,
                'getBranch' => $query->getBranch?->unvan,
                'getMarka' => $query->getCar?->getMarka?->name,
                'getModel' => $query->getCar?->getModel?->name,
                'belge_no' => $query->belge_no,
                'model_yili' => $query->getCar?->model_yili,
                'created_at'=>$query->created_at->format('d.m.Y H:i:s'),
                'cikis_tarihi'=>!empty($query->cikis_tarihi) ? date('d.m.Y H:i',strtotime($query->cikis_tarihi)):'',
                'sase_no' => $query->sase_no ? $query->sase_no : $query->getCar?->sase_no,
                'getFuel' => $query->getCar->getFuel->name ??  '',
                'getGear' => $query->getCar->getGear->name ?? '',
                'motor_hacmi' => $query->getCar->motor_hacmi ?? '',
                'plaka' => $query->getCar?->plaka,
                'getBodyworks' => $getBodyworks,
                'bodywork_image' => 'https://umram.online/storage/'.($query->bodywork_image ? $query->bodywork_image : ($query->getCar && $query->getCar->getCaseType ? $query->getCar->getCaseType->image : \App\Models\CarCaseType::where('status',1)->first()->image)),
                'getAlici' => $query->payment_type == 'sozlesme' && $query->getPayment?->getContractCode?->getContract->customer_id != $alici->id ? $query->getPayment?->getContractCode?->getContract?->getCustomer?->fullName . '(' . $alici?->fullName . ')' : $alici?->fullName,
                'getAliciType' => $query->getAlici?->type,
                'getAliciTel' => $query->getAlici?->phone(),
                'getAliciTc' => $query->getAlici?->tc_no ?? '',
                'getAliciVergiNo' => $query->getAlici?->vergi_no ?? '',
                'getAliciEsbis' => $query->getAlici?->esbis_no ?? '',
                'getAliciMersis' => $query->getAlici?->mersis ?? '',
                'getAliciYetkiBelgeNo' => $query->getAlici?->yetki_belge_no ?? '',

                'getSaticiTc' => $query->getSatici?->tc_no ?? '',
                'getSaticiVergiNo' => $query->getSatici?->vergi_no ?? '',
                'getSaticiEsbis' => $query->getSatici?->esbis_no ?? '',
                'getSaticiMersis' => $query->getSatici?->mersis ?? '',
                'getSaticiYetkiBelgeNo' => $query->getSatici?->yetki_belge_no ?? '',


                'getSatici' => $query->getSatici?->fullName,
                'getSaticiType' => $query->getSatici?->type,
                'getSaticiTel' =>$query->getSatici?->phone(),
                'fullAddressAlici' => $query->getAlici?->fullAddress,
                'fullAddressSatici' => $query->getSatici?->fullAddress,
                'getSubControlsAndEngines' => $getSubControlsAndEngines,
                'getInternalControls' => $getInternalControls,
                'getTireAndRims' => $getTireAndRims,
                'getDiagnostics' => $query->getDiagnostics,
                'getComponents' => $getComponents,
                'allNotes' => $allNotes,
                'uuid'=>$query->uuid,
                'kvkk_durum' =>$kvkk_durum,
                'is_it_over' => $is_it_over,
                'qr'=>$qr_code,
                'co2_kacak_testi' => (!empty($query->getStockhasOne) &&  ($query->getStockhasOne?->getStock?->co2 == 1 || $query->getStockhasOne->campaign_id == 261)) ? true:false,
                'on_yanal_kayma' => $brake?->yanal_kayma_on,
                'arka_yanal_kayma' => $brake?->yanal_kayma_arka,
                'on_fren_dengesizlik_orani' => (float) $brake?->dengesizlik_orani_on,
                'arka_fren_dengesizlik_orani' => (float) $brake?->dengesizlik_orani_arka,
                'fren_max_a' => (float) replaceFloat($brake?->max_kuvvet_on_1),
                'fren_max_b' => (float) replaceFloat($brake?->max_kuvvet_arka_1),
                'fren_max_c' => (float) replaceFloat($brake?->max_kuvvet_on_2),
                'fren_max_d' => (float) replaceFloat($brake?->max_kuvvet_arka_2),
                'fren_on_bosta_surtunme_a' => (float) replaceFloat($brake?->on_dingil_bosta_a),
                'fren_on_bosta_surtunme_b' => (float) replaceFloat($brake?->on_dingil_bosta_b),
                'fren_on_yalpa_a' => (float) replaceFloat($brake?->yalpa_orani_on_1),
                'fren_on_yalpa_b' => (float) replaceFloat($brake?->yalpa_orani_on_2),
                'fren_arka_bosta_surtunme_a' => (float) replaceFloat($brake?->arka_dingil_bosta_a),
                'fren_arka_bosta_surtunme_b' => (float) replaceFloat($brake?->arka_dingil_bosta_b),
                'fren_arka_yalpa_a' => (float) replaceFloat($brake?->yalpa_orani_arka_1),
                'fren_arka_yalpa_b' => (float) replaceFloat($brake?->yalpa_orani_arka_2),
                'fren_el_freni_a' => (float) replaceFloat($brake?->el_freni_kuvvet_a),
                'fren_el_freni_b' => (float) replaceFloat($brake?->el_freni_kuvvet_b),
                'fren_el_freni_dengesizlik' => (int) $brake?->el_freni_dengesizlik_orani,
                'suspansiyon_min_tutunma_a' => (float) replaceFloat($brake?->suspansiyon_on_1),
                'suspansiyon_min_tutunma_b' => (float) replaceFloat($brake?->suspansiyon_arka_1),
                'suspansiyon_min_tutunma_c' => (float) replaceFloat($brake?->suspansiyon_on_2),
                'suspansiyon_min_tutunma_d' => (float) replaceFloat($brake?->suspansiyon_arka_2),
                'getStocks' => $stoklar,
                'co2Sonuc' => $co2Sonuc,
            ];
            $logs = QueryLog::where("uuid",$expertise["uuid"])->first();
            return view('pages.expertise.hasar_raporu',compact([
                'expertise','bodyworks1','bodyworks2','bodyworks3','engines1','engines2','logs',
                'settings']));
        }
        else{
            return back()->with('error','Kayıt Bulunamadı!');
        }
    }

    public function ekspertizIslemleri(){
        $authUser = \auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['list_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $savedFilters = ExpertiseSavedFilter::where('user_id',$authUser->id)->get();
        $selectedSavedFilter = isset($_GET['saved_filter_id']) && (int)$_GET['saved_filter_id'] > 0 ? ExpertiseSavedFilter::where(['user_id'=>$authUser->id,'id'=>(int)$_GET['saved_filter_id']])->first() : null;
        $authUserBranchIds = $authUser->getBranchIds();

        $startDate = request('start_date');
        $endDate = request('end_date');

        if (!empty($startDate)) {
            $startDate = Carbon::make($startDate)->format('Y-m-d');
        } else {
            $startDate = Carbon::now()->format('Y-m-d');
        }

        if (!empty($endDate)) {
            $endDate = Carbon::make($endDate)->format('Y-m-d');
        } else {
            $endDate = Carbon::now()->format('Y-m-d');
        }

        $items = Expertise::select('expertises.*')->where('expertises.status','!=',-1)->where('manuel_save',1)->where('status','!=',3)->where('ftp_ok',2);
        //->whereDate('created_at',now()->format('Y-m-d'));

        if ($authUser->user_role_group_id == 33){
            $items = $items->whereHas('getStocks',function ($query){
                return $query->where('id','!=',10)->orWhere('id','!=',186);
            });
        }elseif($authUser->user_role_group_id == 34){
            $items = $items->whereHas('getStocks',function ($query){
                return $query->where('id','!=',9)->orWhere('id','!=',187);
            });
        }

        if(!auth('customer')->check()){
            if (!empty(request('branch_id')) && request('branch_id') != 'all' && in_array(request('branch_id'),$authUserBranchIds)){
                $items = $items->where('branch_id',request('branch_id'));
            }else{
                $items = $items->whereIn('branch_id',$authUserBranchIds);
            }
        }else{
            $items = $items->where('expertises.cari_id',auth('customer')->user()->id);
        }
        if(!empty(request('type'))){
            $items = $items->when(request('search'), function ($query) {
                $type = request('type');

                if ($type == 'customer') {
                    return $query->where(function ($query) {
                        $query->whereHas('getCari', function ($query) {
                            $query->where('unvan', 'like', "%" . request('search') . "%");
                        })->orWhereHas('getSatici', function ($query) {
                            $query->where('unvan', 'like', "%" . request('search') . "%");
                        })->orWhereHas('getAlici', function ($query) {
                            $query->where('unvan', 'like', "%" . request('search') . "%");
                        });
                    });
                } else if ($type == 'plaka') {
                    $searchWithoutSpaces = str_replace(' ','',request('search'));
                    $search  = str_replace(' ','',request('search'));
                    return $query->whereHas('getCar', function ($query) use ($search,$searchWithoutSpaces) {
                        return $query->where('plaka', 'LIKE', "%" . $search . "%")->orWhere('plaka', 'LIKE', "%" . $searchWithoutSpaces . "%");
                    });
                } else {
                    return $query->whereHas('getCar', function ($query) {
                        return $query->where('sase_no', 'LIKE', "%" . request('search') . "%");
                    });
                }
            }) ;
        }
        if(!empty($startDate)){
            $items = $items->whereDate('belge_tarihi', '>=', $startDate);
        }
        if(!empty($endDate)){
            $items = $items->whereDate('belge_tarihi', '<=', $endDate);
        }

        if(!empty(request('belge_no'))){
            $belge_no = request('belge_no');
            $items = $items->where('belge_no','like','%'.$belge_no.'%');
        }



        $items = $items->get();

        // Default filters
        $filters = ['status' => 2];

        if (request('status') === 0 || request('status') === '0') {
            $filters['status'] = 0;
        } elseif (request('status') == 1) {
            $items = $items->filter(function ($item) {
                return $item->isComplete() == true;
            });

            $filters['status'] = 1;
        } elseif (request('status') == 2) {
            $items = $items->filter(function ($item) {
                return $item->isComplete() == false;
            });
        }

        $items = $items->filter(function($item){
            $hizmetTutari ='0₺';
            if(!empty($item->getPayment->type) && $item->getPayment->type == "plus_kart"){
                if(!empty($item->getPayment->plus_card_odeme_id)){
                    $hizmetTutari = !empty($item->getPayment->getPlusCardOdeme->unit_price) ? round($item->getPayment->getPlusCardOdeme->unit_price,2) : '0₺';
                }
            }else{
                $hizmetTutari = count($item->getStocks) > 0 ? round($item->getStocks->sum('hizmet_tutari'),2) : 0;
                $hizmetTutari = number_format((float)$hizmetTutari,2,',','.')."₺";
            }
            return $item->hizmetTutari = $hizmetTutari;
        });
        $items = $items->filter(function($item){
            $co2_tab = 2;

            if (!empty($item->getStockhasOne)
                && in_array(
                    $item->getStockhasOne->stock_id,
                    [209, 217, 218, 220, 221, 222, 224, 225, 226, 227, 228, 229]
                )
            ) {
                $co2_tab = 1;
            }

            if(!empty($item->getStockhasOne->campaign_id) && $item->getStockhasOne->campaign_id == 261){
                $co2_tab = 1;
            }

            return $item->co2_tab = $co2_tab ;
        });
        $items = $items->filter(function($item){
            $sorgu_tab = 2;
            if(!empty($item->getStockhasOne) && $item->getStockhasOne->sorgu_hizmeti == 1){
                $sorgu_tab = 1;
            }
            return $item->sorgu_tab = $sorgu_tab;
        });

        return view('pages.expertise.islemler',[
            'items'=>$items,
            'savedFilters' => $savedFilters,
            'selectedSavedFilter' =>$selectedSavedFilter,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'filters' => $filters,
            'pageLength' => $authUser->isExpert() ? 25 : 10,
        ]);
    }

    public function autoExpertiseSave(Request $request){
        $authUser = \auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['add_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $authUserBranchIds = $authUser->getBranchIds();
        $expertise = Expertise::where(['user_id'=>$authUser->id,'manuel_save'=>0,'status'=>2])->first();
        if (!$expertise){
            $expertise = new Expertise();
            $expertise->uuid = (string) Str::orderedUuid();
            $expertise->user_id = $authUser->id;
            $expertise->belge_tarihi = now()->format('Y-m-d H:i:s');
            $checkBelgeNo = Expertise::where('belge_no',$request->belge_no)->count();
            if ($checkBelgeNo > 0)
                $belgeNo = createBelgeNo();
            else
                $belgeNo = $request->belge_no;

            $expertise->belge_no = $belgeNo;
        }else{
            if ($request->belge_no){
                $checkBelgeNo = Expertise::where('belge_no',$request->belge_no)->where('id','!=',$expertise->id)->count();
                if ($checkBelgeNo > 0)
                    $belgeNo = createBelgeNo();
                else
                    $belgeNo = $request->belge_no;
                $expertise->belge_no = $belgeNo;
            }

        }

        if ($request->cari_id)
            $expertise->cari_id = $request->cari_id;
        if ($request->cari_id)
            $expertise->nereden_ulastiniz = $request->nereden_ulastiniz;
        if ($request->satici_id)
            $expertise->satici_id = $request->satici_id;
        if ($request->alici_id)
            $expertise->alici_id = $request->alici_id;
        if ($request->car_id)
        {
            $car_net_agirlik = Car::where('id', $request->car_id)->pluck('net_agirlik')->first();
            $expertise->car_id = $request->car_id;
            $expertise->sase_no = $request->sase_no;
            $expertise->net_agirlik = $car_net_agirlik;
        }

        if ($request->belge_tarihi)
            $expertise->belge_tarihi = $request->belge_tarihi;

        if ($request->belge_ozel_kodu)
            $expertise->belge_ozel_kodu = $request->belge_ozel_kodu;
        if ($request->sigorta_teklif_ver)
            $expertise->sigorta_teklif_ver = $request->sigorta_teklifi_ver;
        if ($request->yayin_yasagi)
            $expertise->yayin_yasagi = $request->yayin_yasagi;
        if ($request->kayit_branch_id)
        {
            $expertise->kayit_branch_id = $request->kayit_branch_id;
        }else{
            $expertise->kayit_branch_id = $authUserBranchIds[0];
        }

        if ($request->branch_id){
            $expertise->branch_id = $request->branch_id;
        }else{
            $expertise->branch_id = $authUserBranchIds[0];
        }

        if ($request->payment_type)
            $expertise->payment_type = $request->payment_type;
        if ($request->plus_kart_id)
            $expertise->plus_kart_id = $request->plus_kart_id;
        if ($request->plus_card_payment_type)
            $expertise->plus_card_payment_type = $request->plus_card_payment_type;

        $expertise->status =2;
        if ($request->cikis_tarihi && !$authUser->isAdmin())
            $expertise->cikis_tarihi = $request->cikis_tarihi;

        if(!empty($request->km)){
            $expertise->km = $request->km;
            $expertise->km_type = $request->km_type;
        }


        if(!empty($request->stock_id)){

            foreach ($request->stock_id as $key => $stock){

                if(!empty($stock) && $stock != 0){
                    $stock_card = Stock::find($stock);
                    if(!empty($stock_card)){
                        $expertise_stock_new = ExpertiseStock::where('expertise_id',$expertise->id)->where('stock_id',$stock)->first();
                        if(empty($expertise_stock_new)){
                            ExpertiseStock::where('expertise_id',$expertise->id)->delete();
                            $expertise_stock_new = new ExpertiseStock();
                        }
                        $price = !empty($stock_card->getStandartPrices->kdv_dahil_fiyat) ? $stock_card->getStandartPrices->kdv_dahil_fiyat : 0;
                        if(!empty($request->campaign_id[$key]) ){
                            $price = !empty($stock_card->getCampaingPrices->where('stock_id',$stock)->where('campaign_id',$request->campaign_id[$key])->first()->kdv_dahil_fiyat) ? $stock_card->getCampaingPrices->where('stock_id',$stock)->where('campaign_id',$request->campaign_id[$key])->first()->kdv_dahil_fiyat : $stock_card->getStandartPrices->kdv_dahil_fiyat;
                        }

                        $expertise_stock_new->expertise_id = $expertise->id;
                        $expertise_stock_new->stock_id = $stock;
                        $expertise_stock_new->campaign_id = !empty($request->campaign_id[$key]) ? $request->campaign_id[$key]:0;
                        $expertise_stock_new->sorgu_hizmeti = $stock_card->sorgu_hizmeti;
                        $expertise_stock_new->iskonto_amount = !empty($request->iskonto_amount[$key]) ? $request->iskonto_amount[$key]:0;//$request->iskonto_amount;
                        $expertise_stock_new->hizmet_tutari = $price - (int)$request->iskonto_amount;
                        $expertise_stock_new->liste_fiyati = $price;
                        $expertise_stock_new->save();
                    }
                }
            }
        }

        $expertise->save();
        $odeme_tip_array = array();
        if ($request->odeme_tip && !empty($request->odeme_tip[0])){

            foreach ($request->odeme_tip as $key => $tip){
                $temizVeri = str_replace('₺', '', $request->odeme_tutar[$key]);
                $temizVeri = str_replace('.', '', $temizVeri);
                $temizVeri = str_replace(',','.', $temizVeri);

                $expertisePayment = ExpertisePayment::where('expertise_id',$expertise->id)
                    ->where('type',$request->odeme_tip[$key])
                    ->first();
                $odeme_tip_array[] = $request->odeme_tip[$key];
                if(empty($expertisePayment)){
                    $expertisePayment = new ExpertisePayment();
                }
                $expertisePayment->expertise_id = $expertise->id;
                $expertisePayment->case_id = $request->odeme_hesap[$key];
                $expertisePayment->amount = (float) $temizVeri;
                $expertisePayment->type = $request->odeme_tip[$key];
                $expertisePayment->save();
                logRecord(
                    "add_expertise_payment_for_expertise",
                    $authUser->name . " adlı kullanıcı ".$expertise->getCari?->unvan." adlı müşterinin ekspertiz kaydına ".(float)str_replace(',','.',$request->odeme_tutar[$key]). "₺ ödeme ekledi.",
                    $expertisePayment->id
                );
            }
        }

        $expertisePayment = ExpertisePayment::where('expertise_id',$expertise->id)
            ->where('type','!=','plus_kart')
            ->get();
        foreach($expertisePayment as $ep){
            if(!in_array($ep->type,$odeme_tip_array)){
                $ep->delete();
            }
        }


        return response()->json(['success'=>'true','expertise_uuid'=>$expertise->uuid]);
    }

    public function export(){
        $authUser = auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['download_excel_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $type = $_GET['type'] ?? null;
        $search = $_GET['search'] ?? null;
        $start_date = $_GET['start_date'] ?? date('Y-m-d');
        $end_date = $_GET['end_date'] ?? date('Y-m-d');
        $belge_no = $_GET['belge_no'] ?? null;
        $branch_id = $_GET['branch_id'] ?? null;
        $telephone = $authUser->telephone ?? '';
        $uuid = $_GET['uuid'] ?? null;
        $payment_type = isset($_GET['payment_type']) && $_GET['payment_type'] != '' ? $_GET['payment_type'] : 'all';
        $expertise_campaing = isset($_GET['expertise_campaing']) && $_GET['expertise_campaing'] != '' ? $_GET['expertise_campaing'] : 'all';
        $customerId = $_GET['customer_id'] ?? null;
        $expertise_campaing = $_GET['expertise_campaing'] ?? null;
        if(empty($start_date)){
            $start_date = date('Y-m-d');
        }
        if(empty($end_date)){
            $end_date = date('Y-m-d');
        }

        $filters = [
            'type' => $type,
            'search' => $search,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'belge_no' => $belge_no,
            'branch_id' => $branch_id,
            'random_uui' => rand(0000000, 9999999),
            'telephone' => $telephone,
            'uuid' => $uuid,
            'payment_type' => $payment_type,
            'customerId' => $customerId,
            'expertise_campaing' => $expertise_campaing,
        ];

        ExpertisesExportJob::dispatch($filters, auth()->user());

        return redirect()->back()->with('success','Excel İndirme kodu sms olarak gönderilecektir.');
    }

    public function deleteExpertise(Request $request)
    {
        if ((int)$request->expertise_id > 0){
            $expertise = Expertise::where(['id'=>(int)$request->expertise_id,'manuel_save'=>0,'status'=>2])->first();
            if(!empty($expertise)){
                $expertise->delete();
                ExpertiseStock::where('expertise_id',(int)$request->expertise_id)->delete();
                ExpertisePayment::where('expertise_id',(int)$request->expertise_id)->delete();
            }

        }


        return redirect()->route('expertises.create');
    }

    public function updateExpertiseNote(Request $request){
        if($request->type == "brake"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $experBrake = ExpertiseBrake::where('expertise_id',$expertises->id)->first();
                if(!empty($experBrake)){
                    $selectedNotes = json_decode($request->selectedNotes);
                    foreach($selectedNotes as $newNotes){
                        $new_notes = new ExpertiseBrakeNote();
                        $new_notes->expertise_brake_id = $experBrake->id;
                        $new_notes->note = $newNotes;
                        $new_notes->save();
                    }
                    return response()->json(['success'=>'true','ExperBrakeNote'=>$experBrake?->getNotes]);
                }else{
                    return response()->json(['success'=>'false']);
                }
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "bodywork"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $new_notes = new ExpertiseBodyworkNote();
                    $new_notes->expertise_id = $expertises->id;
                    $new_notes->note = $newNotes;
                    $new_notes->save();
                }
                return response()->json(['success'=>'true','ExperBodyworkNote'=>$expertises?->getBodyworkNotes]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "diagnostic"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $new_notes = new ExpertiseDiagnosticNote();
                    $new_notes->expertise_id = $expertises->id;
                    $new_notes->note = $newNotes;
                    $new_notes->save();
                }
                return response()->json(['success'=>'true','ExperDiagnosticNote'=>$expertises?->getDiagnosticNotes]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "internal_controls"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $new_notes = new ExpertiseInternalControlsNote();
                    $new_notes->expertise_id = $expertises->id;
                    $new_notes->note = $newNotes;
                    $new_notes->save();
                }
                return response()->json(['success'=>'true','ExperInternalControlsNote'=>$expertises?->getInternalControlsNotes]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "tire_and_rim"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $new_notes = new ExpertiseTireAndRimNote();
                    $new_notes->expertise_id = $expertises->id;
                    $new_notes->note = $newNotes;
                    $new_notes->save();
                }
                return response()->json(['success'=>'true','ExperTireAndRimNote'=>$expertises?->getTireAndRimNotes]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "sub_controls_and_engine"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $new_notes = new ExpertiseSubControlAndEngineNote();
                    $new_notes->expertise_id = $expertises->id;
                    $new_notes->note = $newNotes;
                    $new_notes->save();
                }
                return response()->json(['success'=>'true','ExperSubControlsAndEngineNote'=>$expertises?->getSubControlsAndEngineNotes]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "component"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $new_notes = new ExpertiseComponentNote();
                    $new_notes->expertise_id = $expertises->id;
                    $new_notes->note = $newNotes;
                    $new_notes->save();
                }
                return response()->json(['success'=>'true','ExperComponentNote'=>$expertises?->getComponentNotes]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "co2"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $new_notes = new ExpertiseCo2Note();
                    $new_notes->expertise_id = $expertises->id;
                    $new_notes->note = $newNotes;
                    $new_notes->save();
                }
                return response()->json(['success'=>'true','ExperCo2Note'=>$expertises?->getCo2Notes]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
    }

    public function deleteExpertiseNote(Request $request){
        if($request->type == "brake"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $experBrake = ExpertiseBrake::where('expertise_id',$expertises->id)->first();
                if(!empty($experBrake)){
                    $selectedNotes = json_decode($request->selectedNotes);
                    foreach($selectedNotes as $newNotes){
                        $expertiseBrakeNote = ExpertiseBrakeNote::where('id',$newNotes)->first();
                        if(!empty($expertiseBrakeNote)){
                            $expertiseBrakeNote->delete();
                        }
                    }
                    $BrakeAllNotes = Note::where('key','brake')->get();
                    $ExperBrakeNote = $experBrake?->getNotes;

                    $HaveExperId = array();
                    foreach($ExperBrakeNote as $have_note){
                        array_push($HaveExperId,$have_note->note);
                    }
                    $selectedNote = array();
                    foreach($BrakeAllNotes as $brake_note){
                        if(!in_array($brake_note->note,$HaveExperId)){
                            array_push($selectedNote,$brake_note->note);
                        }
                    }

                    return response()->json(['success'=>'true','ExperBrakeNote'=>$experBrake?->getNotes,'selectedNote'=>$selectedNote]);
                }else{
                    return response()->json(['success'=>'false']);
                }
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "bodywork"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $expertiseBodyworkNote = ExpertiseBodyworkNote::where('id',$newNotes)->first();
                    if(!empty($expertiseBodyworkNote)){
                        $expertiseBodyworkNote->delete();
                    }
                }
                $BodyworkAllNotes = Note::where('key','bodywork')->get();
                $ExperBrakeNote = $expertises->getBodyworkNotes;

                $HaveExperId = array();
                foreach($ExperBrakeNote as $have_note){
                    array_push($HaveExperId,$have_note->note);
                }
                $selectedNote = array();
                foreach($BodyworkAllNotes as $bodywork_note){
                    if(!in_array($bodywork_note->note,$HaveExperId)){
                        array_push($selectedNote,$bodywork_note->note);
                    }
                }

                return response()->json(['success'=>'true','ExperBodyworkNote'=>$expertises?->getBodyworkNotes,'selectedNote'=>$selectedNote]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "diagnostic"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $expertiseDiagnosticNote = ExpertiseDiagnosticNote::where('id',$newNotes)->first();
                    if(!empty($expertiseDiagnosticNote)){
                        $expertiseDiagnosticNote->delete();
                    }
                }
                $DiagnosticNotes = Note::where('key','diagnostic')->get();
                $ExperDiagnosticNote = $expertises->getDiagnosticNotes;

                $HaveExperId = array();
                foreach($ExperDiagnosticNote as $have_note){
                    array_push($HaveExperId,$have_note->note);
                }
                $selectedNote = array();
                foreach($DiagnosticNotes as $diagnostic_note){
                    if(!in_array($diagnostic_note->note,$HaveExperId)){
                        array_push($selectedNote,$diagnostic_note->note);
                    }
                }

                return response()->json(['success'=>'true','ExperDiagnosticNote'=>$expertises?->getDiagnosticNotes,'selectedNote'=>$selectedNote]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "internal_controls"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $expertiseInternalControlsNote = ExpertiseInternalControlsNote::where('id',$newNotes)->first();
                    if(!empty($expertiseInternalControlsNote)){
                        $expertiseInternalControlsNote->delete();
                    }
                }
                $InternalControlsNotes = Note::where('key','internal')->get();
                $InternalControlsNote = $expertises->getInternalControlsNotes;

                $HaveExperId = array();
                foreach($InternalControlsNote as $have_note){
                    array_push($HaveExperId,$have_note->note);
                }
                $selectedNote = array();
                foreach($InternalControlsNotes as $internal_controls){
                    if(!in_array($internal_controls->note,$HaveExperId)){
                        array_push($selectedNote,$internal_controls->note);
                    }
                }

                return response()->json(['success'=>'true','ExperInternalControlsNote'=>$expertises?->getInternalControlsNotes,'selectedNote'=>$selectedNote]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "tire_and_rim"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $expertiseTireAndRimNote = ExpertiseTireAndRimNote::where('id',$newNotes)->first();
                    if(!empty($expertiseTireAndRimNote)){
                        $expertiseTireAndRimNote->delete();
                    }
                }
                $TireAndRimNotes = Note::where('key','tire_and_rim')->get();
                $TireAndRimNote = $expertises->getTireAndRimNotes;

                $HaveExperId = array();
                foreach($TireAndRimNote as $have_note){
                    array_push($HaveExperId,$have_note->note);
                }
                $selectedNote = array();
                foreach($TireAndRimNotes as $tire_and_rim){
                    if(!in_array($tire_and_rim->note,$HaveExperId)){
                        array_push($selectedNote,$tire_and_rim->note);
                    }
                }

                return response()->json(['success'=>'true','ExperTireAndRimNote'=>$expertises?->getTireAndRimNotes,'selectedNote'=>$selectedNote]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "sub_controls_and_engine"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $expertiseSubControlsAndEngineNote = ExpertiseSubControlAndEngineNote::where('id',$newNotes)->first();
                    if(!empty($expertiseSubControlsAndEngineNote)){
                        $expertiseSubControlsAndEngineNote->delete();
                    }
                }
                $SubControlsAndEngineNotes = Note::where('key','sub_control')->get();
                $SubControlsAndEntineNote = $expertises->getSubControlsAndEngineNotes;

                $HaveExperId = array();
                foreach($SubControlsAndEntineNote as $have_note){
                    array_push($HaveExperId,$have_note->note);
                }
                $selectedNote = array();
                foreach($SubControlsAndEngineNotes as $sub_controls_and_engine){
                    if(!in_array($sub_controls_and_engine->note,$HaveExperId)){
                        array_push($selectedNote,$sub_controls_and_engine->note);
                    }
                }

                return response()->json(['success'=>'true','ExperSubControlsAndEngineNote'=>$expertises?->getSubControlsAndEngineNotes,'selectedNote'=>$selectedNote]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "component"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $expertiseComponentEngineNote = ExpertiseComponentNote::where('id',$newNotes)->first();
                    if(!empty($expertiseComponentEngineNote)){
                        $expertiseComponentEngineNote->delete();
                    }
                }
                $ComponentNotes = Note::where('key','sub_control')->get();
                $ComponentNote = $expertises->getComponentNotes;

                $HaveExperId = array();
                foreach($ComponentNote as $have_note){
                    array_push($HaveExperId,$have_note->note);
                }
                $selectedNote = array();
                foreach($ComponentNotes as $component_note){
                    if(!in_array($component_note->note,$HaveExperId)){
                        array_push($selectedNote,$component_note->note);
                    }
                }

                return response()->json(['success'=>'true','ExperComponentNote'=>$expertises?->getComponentNotes,'selectedNote'=>$selectedNote]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
        if($request->type == "co2"){
            $expertises = Expertise::where('uuid',$request->expertise_id)->first();
            if(!empty($expertises)){
                $selectedNotes = json_decode($request->selectedNotes);
                foreach($selectedNotes as $newNotes){
                    $expertiseComponentEngineNote = ExpertiseCo2Note::where('id',$newNotes)->first();
                    if(!empty($expertiseComponentEngineNote)){
                        $expertiseComponentEngineNote->delete();
                    }
                }
                $ComponentNotes = Note::where('key','co2')->get();
                $ComponentNote = $expertises->getComponentNotes;

                $HaveExperId = array();
                foreach($ComponentNote as $have_note){
                    array_push($HaveExperId,$have_note->note);
                }
                $selectedNote = array();
                foreach($ComponentNotes as $component_note){
                    if(!in_array($component_note->note,$HaveExperId)){
                        array_push($selectedNote,$component_note->note);
                    }
                }

                return response()->json(['success'=>'true','ExperComponentNote'=>$expertises?->getComponentNotes,'selectedNote'=>$selectedNote]);
            }else{
                return response()->json(['success'=>'false']);
            }

        }
    }

    public function ekspertizFtpView($uuid){
        $dosyaAdi = 'expertises_ftp/'.$uuid.'/masked/'.$uuid.'.html';
        $icerik = Storage::get($dosyaAdi);
        echo $icerik;
    }

    public function setExpertiseDownloaded(Request $request)
    {
        if (\auth()->user()->isAdmin())
            return response()->json(['success'=>'false']);

        $expertise = Expertise::where('uuid',$request->uuid)->first();
        if ($expertise && $expertise->employee_downloaded == 0 && $expertise->isComplete()){
            $expertise->employee_downloaded = 1;
            $expertise->save();
        }
        if(!empty($expertise->ftp_ok) && $expertise->ftp_ok == 2){
            $expertise->cikis_tarihi = date('Y-m-d H:i:s');
            $expertise->save();
        }
        return response()->json(['success'=>'true']);
    }

    public function taslakAracResmi($uuid, ExpertiseBodyworkService $expertiseBodyworkService)
    {
        $query = Expertise::where('uuid',$uuid)->first();
        $expertise = [];
        $bodyworks1 = array_slice(__('arrays.bodyworks'), 0, 8);
        $bodyworks2 = array_slice(__('arrays.bodyworks'), 8, 8);

        $getBodyworks = [];

// Retrieve all bodywork records for the given expertise_id in one query
        $bodyworks = ExpertiseBodywork::where('expertise_id', $query->id)->get()->keyBy('key');

        foreach (__('arrays.bodyworks') as $key => $bodywork) {
            $getBodywork = $bodyworks->get($key);
            $getBodyworks[$key] = [
                'orijinal' => $getBodywork?->orijinal ?? 0,
                'boyali' => $getBodywork?->boyali ?? 0,
                'degisen' => $getBodywork?->degisen ?? 0,
                'duz' => $getBodywork?->duz ?? 0,
                'note' => $getBodywork?->note ?? '',
                'title' => $bodywork
            ];
        }


        if ($query){


//            $img = Image::make(public_path('/storage/'.$query->getCar?->getCaseType?->bodywork_open_image));
////            $img->resize(662,596);
//            $img->rotate(-90);
////            $img->text('<div style="width: 40px;height: 40px;background-color: #0a53be">sdfdsf</div>', 120, 100, function($font) {
////                $font->file(5);
////                $font->size(1);
////                $font->color('#ff1000');
////            });
//
//            $img->save(public_path('/storage/'.$query->getCar?->getCaseType?->bodywork_open_image));

            $expertise = [
                'bodywork_open_image' => '/storage/'.$query->getCar?->getCaseType?->bodywork_open_image,
                'getBodyworks' => $getBodyworks,
                'getBranch' => $query->getBranch?->unvan,
                'getMarka' => $query->getCar?->getMarka?->name,
                'getModel' => $query->getCar?->getModel?->name,
                'belge_no' => $query->belge_no,
                'km' => $query->km,
                'model_yili' => $query->getCar?->model_yili,
                'created_at'=>$query->created_at->format('d.m.Y H:i:s'),
                'cikis_tarihi'=>!empty($query->cikis_tarihi) ? date('d.m.Y',strtotime($query->cikis_tarihi)):'',
                'sase_no' => $query->sase_no ?? $query->getCar?->sase_no,
                'getFuel' => $query->getCar->getFuel->name ??  '',
                'getGear' => $query->getCar->getGear->name ?? '',
                'motor_hacmi' => $query->getCar->motor_hacmi ?? '',
                'plaka' => $query->getCar?->plaka,
                'coordinates' => $expertiseBodyworkService->getBodyworkCoordinates($query, $getBodyworks),
            ];
        }

        return view('pages.expertise.bodywork_open',compact([
            'expertise',
            'bodyworks1',
            'bodyworks2'
        ]));
    }

    public function deleteExpertisePayment(Request $request){
        $expertise_payment = ExpertisePayment::find($request->payment_id);
        if(!empty($expertise_payment)){
            $expertise_payment->delete();
            return response()->json(['success'=>'true']);
        }else{
            return response()->json(['success'=>'false']);
        }
    }

    public function addBodyworkCoordinate(Request $request)
    {
        $expertise = Expertise::where('uuid',$request->expertise_uuid)->select('id')->first();
        if ($expertise){
            $bodyworkCoordinate = BodyworkCoordinate::where('expertise_id',$expertise->id)->first();
            if (!$bodyworkCoordinate)
                $bodyworkCoordinate = new BodyworkCoordinate();
            $bodyworkCoordinate->expertise_id = $expertise->id;
            $bodyworkCoordinate->user_id = \auth()->user()->id;
            $bodyworkCoordinate->coordinates = $request->coordinates;
            $bodyworkCoordinate->image_url = $request->imageVersion;
            $bodyworkCoordinate->image_client_width = $request->image_client_width;
            $bodyworkCoordinate->image_client_height = $request->image_client_height;
            $bodyworkCoordinate->save();

            return response()->json(['success'=>'true'],200);
        }

        return response()->json(['success'=>'false'],200);
    }

    public function plusCardSmsVerification(Request $request)
    {
        if ((int)$request->plus_card_id < 1) {
            return response()->json(['success' => 'false', 'message' => 'Plus Card Seçilmedi']);
        }

// Optimized single query to get PlusCard and Customer details
        $plusCardCustomer = PlusCard::join('customers', 'plus_cards.customer_id', '=', 'customers.id')
            ->where('plus_cards.id', $request->plus_card_id)
            ->select('plus_cards.id as plus_card_id', 'plus_cards.customer_id', 'customers.cep', 'customers.telefon')
            ->first();

        if (!$plusCardCustomer) {
            return response()->json(['success' => 'false', 'message' => 'Plus Card veya Cari Bulunamadı!']);
        }

// Cache settings efficiently
        $settings = Cache::remember('settings', 60 * 60, function () {
            return Setting::first();
        });

        if ($plusCardCustomer->cep) {
            $telefon = str_replace(['(', ')', ' '], '', $plusCardCustomer->cep);
            $telefon_format = formatTelephoneNumber($telefon);

            // Send SMS only if the phone number is valid
            if ($telefon_format) {
                sendSmsOTP(
                    $settings->netgsm_usercode,
                    $settings->netgsm_password,
                    $settings->netgsm_msgheader3,
                    "1 adet plus card harcamanız için kod $request->sms_code_cep",
                    $telefon_format
                );
            }
        }

        if ($plusCardCustomer->telefon && $plusCardCustomer->telefon != $plusCardCustomer->cep) {
            $telefon = str_replace(['(', ')', ' '], '', $plusCardCustomer->telefon);
            $telefon_format = formatTelephoneNumber($telefon);



            // Send SMS only if the phone number is valid
            if ($telefon_format) {
                sendSmsOTP(
                    $settings->netgsm_usercode,
                    $settings->netgsm_password,
                    $settings->netgsm_msgheader3,
                    "1 adet plus card harcamanız için kod $request->sms_code_telefon",
                    $telefon_format
                );
            }
        }


        return response()->json(['success'=>'true','message'=>'Sms Gönderildi','plusCardCustomerCep'=>$plusCardCustomer->cep,'plusCardCustomerTelefon'=>$plusCardCustomer->telefon]);
    }

    public function expertiseReportDownload(Request $request) :void
    {
        /** @var User $user */
        $user = auth()->user();

        if (!$user->isAdmin()) {
            $reportDownload = ExpertiseReportDownload::where('e_uuid', $request->uuid)
                ->where('u_id', $user->id)
                ->where('created_at', '>=', now()->subMinutes(2))
                ->exists();

            if (!$reportDownload) {
                $reportDownload = new ExpertiseReportDownload();
                $reportDownload->e_uuid = $request->uuid;
                $reportDownload->u_id = $user->id;
                $reportDownload->created_at = now();
                $reportDownload->save();
            }
        }
    }
}
