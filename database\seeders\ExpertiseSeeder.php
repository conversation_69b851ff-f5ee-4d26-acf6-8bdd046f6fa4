<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ExpertiseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        // Insert into expertises table
        $expertiseId = DB::table('expertises')->insertGetId([
            'old_id' => null,
            'uuid' => '9f8587ae-ed75-4f37-9551-82dcf6a00253',
            'user_id' => 1423,
            'kayit_branch_id' => 26,
            'branch_id' => 26,
            'cari_id' => 1983293,
            'satici_id' => 1983293,
            'alici_id' => 1983294,
            'car_id' => 64,
            'km' => 18640,
            'sase_no' => 'WAUCZZ8V5CA012345',
            'km_type' => 1,
            'net_agirlik' => '1750',
            'nereden_ulastiniz' => 'yok',
            'sorgu_hizmeti' => null,
            'belge_tarihi' => $now,
            'belge_no' => '2500113420731',
            'belge_ozel_kodu' => 1,
            'sigorta_teklif_ver' => null,
            'yayin_yasagi' => 0,
            'kayit_yeri' => null,
            'status' => 1,
            'odeme_turu' => null,
            'bodywork_image' => null,
            'diagnostic_file' => null,
            'arac_kontrol_user' => 0,
            'arac_kontrol' => 0,
            'fren_kontrol_user' => 0,
            'fren_kontrol' => 0,
            'kaporta_kontrol_user' => 0,
            'kaporta_kontrol' => 0,
            'diagnostic_kontrol_user' => 0,
            'diagnostic_kontrol' => 0,
            'ic_kontrol_user' => 0,
            'ic_kontrol' => 0,
            'lastik_jant_kontrol_user' => 0,
            'lastik_jant_kontrol' => 0,
            'alt_motor_kontrol_user' => 0,
            'alt_motor_kontrol' => 0,
            'komponent_kontrol_user' => 0,
            'komponent_kontrol' => 0,
            'co2_kontrol_user' => 0,
            'co2_kontrol' => 0,
            'hasar_sorgu_sonuc' => null,
            'kilometre_sorgu_sonuc' => null,
            'borc_sorgu_sonuc' => null,
            'ruhsat_sorgu_sonuc' => null,
            'payment_type' => 'normal',
            'plus_card_payment_type' => null,
            'plus_kart_id' => null,
            'audio_url' => null,
            'cikis_tarihi' => null,
            'manuel_save' => 1,
            'employee_downloaded' => 0,
            'ftp_ok' => 2,
            'ftp_date' => null,
            'created_at' => $now,
            'updated_at' => $now,
            'deleted_at' => null,
        ]);

        // Insert into expertise_stocks table
        DB::table('expertise_stocks')->insert([
            [
                'expertise_id' => $expertiseId,
                'stock_id' => 218,
                'campaign_id' => 0,
                'sorgu_hizmeti' => 1,
                'yol_yardimi' => 1,
                'iskonto_amount' => 0.00,
                'hizmet_tutari' => 8350.00,
                'liste_fiyati' => 8350.00,
                'created_at' => $now,
                'updated_at' => $now,
                'deleted_at' => null,
            ],
            [
                'expertise_id' => $expertiseId,
                'stock_id' => 218,
                'campaign_id' => 0,
                'sorgu_hizmeti' => 1,
                'yol_yardimi' => 1,
                'iskonto_amount' => 0.00,
                'hizmet_tutari' => 8350.00,
                'liste_fiyati' => 8350.00,
                'created_at' => $now,
                'updated_at' => $now,
                'deleted_at' => $now,
            ],
        ]);

        // Insert into expertise_payments table
        DB::table('expertise_payments')->insert([
            'expertise_id' => $expertiseId,
            'case_id' => 0,
            'amount' => 8350.00,
            'type' => 'nakit',
            'payment_code' => null,
            'payment_detail' => null,
            'plus_card_id' => 0,
            'plus_card_odeme_id' => 0,
            'result' => null,
            'created_at' => $now,
            'updated_at' => $now,
            'deleted_at' => null,
            'update_data' => 2,
        ]);
    }
}
