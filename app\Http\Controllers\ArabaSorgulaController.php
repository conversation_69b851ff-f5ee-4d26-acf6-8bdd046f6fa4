<?php

namespace App\Http\Controllers;

use App\Http\Middleware\Auth;
use App\Models\Setting;
use Illuminate\Http\Request;
use App\Models\QueryLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class ArabaSorgulaController extends Controller
{
    public $isLive = true;
    public $url;
    public $userName;
    public $password;
    public $kilometerQueryPrice;
    public $damageQueryPrice;
    public $replacementQueryPrice;
    public $detailQueryPrice;
    public $debtQueryPrice;

    public function __construct()
    {
        if ($this->isLive){
            $this->url = "https://production.arabasorgula.com/v1";
            $this->userName = 'umran';
            $this->password = '4Nf8Zq7P2rB6Xy1D';
        }else{
            $this->url = "https://test.arabasorgula.com/v1";
            $this->userName = 'umran';
            $this->password = 'umran';
        }
        $this->kilometerQueryPrice = Cache::get('kilometerQueryPrice') ?? 0;
        $this->damageQueryPrice = Cache::get('damageQueryPrice') ?? 0;
        $this->replacementQueryPrice = Cache::get('replacementQueryPrice') ?? 0;
        $this->detailQueryPrice = Cache::get('detailQueryPrice') ?? 0;
        $this->debtQueryPrice = Cache::get('debtQueryPrice') ?? 0;
    }

    /**
     * Display a listing of the resource.
     */

    public function index($plaka = null,$sasi = null)
    {

    }


    /**
     * API Token Alma ve Cache Kontrolü Fonksiyonu
     */
    private function getApiToken()
    {
        // Token daha önce alınmışsa ve süresi dolmamışsa cache'den al
        $cachedToken = Cache::get('api_token');
        $expiryDate = Cache::get('token_expiry_date');

        if ($cachedToken && $expiryDate && now()->lessThanOrEqualTo($expiryDate)) {
            return $cachedToken;
        }

        // Token alma isteği
        $url = $this->url.'/auth/generate-token';
        $response = Http::post($url, [
            'username' => $this->userName,
            'password' => $this->password,
        ]);

        if ($response->successful() && $response['isSuccess']) {
            $token = $response['token'];
            $expiryDate = $response['expiryDate'];

            // Token ve expire süresini cache'de sakla
            Cache::put('api_token', $token, now()->addMinutes(60));  // Token cache'le
            Cache::put('kilometerQueryPrice', $response['kilometerQueryPrice'], now()->addMinutes(60));  // Token cache'le
            Cache::put('damageQueryPrice', $response['damageQueryPrice'], now()->addMinutes(60));  // Token cache'le
            Cache::put('replacementQueryPrice', $response['replacementQueryPrice'], now()->addMinutes(60));  // Token cache'le
            Cache::put('detailQueryPrice', $response['detailQueryPrice'], now()->addMinutes(60));  // Token cache'le
            Cache::put('debtQueryPrice', $response['debtQueryPrice'], now()->addMinutes(60));  // Token cache'le
            Cache::put('token_expiry_date', $expiryDate, now()->addMinutes(60));  // Expiry date cache'le

            $this->kilometerQueryPrice = $response['kilometerQueryPrice'];
            $this->damageQueryPrice = $response['damageQueryPrice'];
            $this->replacementQueryPrice = $response['replacementQueryPrice'];
            $this->detailQueryPrice = $response['detailQueryPrice'];
            $this->debtQueryPrice = $response['debtQueryPrice'];

            return $token;
        }

        throw new \Exception('Token alınamadı: ' . $response['Message']);
    }

    /**
     * API Token Alma ve Cache Kontrolü Fonksiyonu
     */

    public function hasar($uuid = null, $plaka = null, $type = null, $tc = null, $date = null,$degisenValue = null)
    {
        // API'ye hangi sorgu tipi için gideceğimizi belirleyen bir mapping
        $typeMapping = [
            'hasar' => ['url' => $this->url.'/sbm/damage','queryType' => 0,'db_name'=>'hasar'], // Hasar Sorgusu
            'kilometre' => ['url' => $this->url.'/kilometer', 'queryType' => '0','db_name'=>'kilometre'], // Kilometre Sorgusu
//            'borc' => ['url' => $this->>$this->url.'/Debt', 'queryType' => null], // Borç Sorgusu
            'AracDetay' => ['url' => $this->url.'/sbm/detail', 'queryType' => 0,'db_name'=>'detail'], // Araç Detay Sorgusu
            'Degisen' => ['url' => $this->url.'/sbm/replacement', 'queryType' => 0,'db_name'=>'degisen'], // Değişen Parça Sorgusu
        ];

        // Sorgu tipi yanlış veya desteklenmiyorsa hatayı döndür
        if (!isset($typeMapping[$type])) {
            return response()->json(['error' => 'Geçersiz sorgu tipi'], 400);
        }

        $authUser = \auth()->user();
        $data = QueryLog::where("uuid", $uuid)->first();




        // Eğer veritabanında daha önce yapılmış bir sorgu varsa onu döndür
        if ($data && !is_null($data->{$typeMapping[$type]['db_name']})) {
            $lastExtraQuery = DB::table('query_logs_extra')
                ->where('expertise_uuid', $uuid)
                ->where('type',$typeMapping[$type]['db_name'])
                ->orderBy('id','desc')
                ->first();
            if ($lastExtraQuery){
                $jsonData = json_decode($lastExtraQuery->result, true);
                if ($type == 'Degisen')
                    return response()->json($jsonData);
                else
                    return response()->json($jsonData['data']);
            }else{
                $jsonData = json_decode($data->{$typeMapping[$type]['db_name']}, true);
                if ($type == 'Degisen')
                    return response()->json($jsonData);
                else
                    return response()->json($jsonData['data']);
            }
        }

        // Token alalım
        $token = $this->getApiToken();

        // İstek yapacağımız URL ve QueryType, typeMapping'den alınıyor
        $apiEndpoint = $typeMapping[$type]['url'];
        $queryType = $typeMapping[$type]['queryType'];

        // Eğer tarih verilmemişse, bugünün tarihi kullanılır
        if ($date == null) {
            $date = date("Y-m-d");
        }

        // İstek yapacağımız veri seti
        $postData = [
            "queryText"=> $plaka,
            "branchCode"=> $authUser->getBranch->kod ?? '',
        ];

        // Eğer queryType varsa onu da post verilerine ekleyelim (borç sorgusunda yok)
        if ($queryType !== null) {
            $postData['QueryType'] = (int)$queryType;  // QueryType'ı API'nin beklediği formatta gönderiyoruz
        }
        if ($type == 'Degisen'){
            $postData['damageDateKeyValue'] = $degisenValue;
            $postData['damageDate'] = $date;
        }

        // Laravel HTTP Client ile API isteği yapıyoruz
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
        ])
            ->timeout(120)
            ->post($apiEndpoint, $postData);
        // Gelen yanıtı JSON olarak alıyoruz
        $res = json_decode($response->body());

        $settings = Setting::first();

        // Başarılı bir sorgu döndüyse veriyi işliyoruz
        if (isset($res["isSuccess"]) && $res["isSuccess"] && !in_array($res['resultCode'],['01','03'])) {
            // Sorgu tipine göre farklı işlemler yapılır (hasar, kilometre, borç vb.)
            if ($type == "hasar") {
                $hasarResponse = [
                    "success" => $res["isSuccess"],
                    "code" => (int) $res["resultCode"],
                    "message" => $res["message"],
                    "data" => [
                        "id" => $res["sbmQueryResultId"],
                        "uniqueId" => $res["processNumber"],
                        "query" => "Hasar Sorgu Plaka",
                        "date" => now(),
                        "data" => $plaka,
                        "userid" => \auth()->id(),  // Kullanıcı ID
                        "result" => $res["message"],
                        "amount" => $res['price'] > 0 ? $res['price'] : $this->damageQueryPrice,
                        "model" => "",
                        "imageurl" => $res["resultImageUrl"],
                        "damageDateList" => [
                            "success" => true,
                            "code" => $res["resultCode"],
                            "message" =>$res["message"],
                            "total" => $res["damageCount"],
                            "data" => array_map(function($replacement) use ($res) {
                                return [
                                    "id" => $res["sbmQueryResultId"],
                                    "queryid" => $res["sbmQueryResultId"],
                                    "date" => $replacement["damageDate"],
                                    "changingparts" => $replacement["hasReplacement"],
                                    "damageDateKeyValue" => $replacement["damageDateKeyValue"],
                                ];
                            }, $res["replacementDetails"] ?? [])
                        ],
                        "branchCode" => $res["branchCode"],
                    ],
                    "from" => "arabasorgula.com"
                ];


                $amount = $res['price'] > 0 ? $res['price'] : $this->damageQueryPrice ?? 0;
                if ($data !== null) {
                    if (!is_null($data->hasar)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type]['db_name'],
                            'result' => json_encode($hasarResponse),
                            'amount' => $amount,
                            'commission' => $amount > 0 ? $settings->hasar_sorgu_komisyon_arabasorgula : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $data->update([
                            "user_id" => $authUser->id,
                            "hasar" => json_encode($hasarResponse),
                            "hasar_ucret" => $amount,
                            "hasar_komisyon" => $amount > 0 ? $settings->hasar_sorgu_komisyon_arabasorgula : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "hasar" => json_encode($hasarResponse),
                        "hasar_ucret" => $amount,
                        "hasar_komisyon" => ($amount) > 0 ? $settings->hasar_sorgu_komisyon_arabasorgula : 0,
                    ]);
                }
                if ($amount != 0 && $amount != $settings->hasar_sorgu_arabasorgula) {
                    $settings->hasar_sorgu_arabasorgula = $amount;
                    $settings->save();
                }
                $res = $hasarResponse;
            }
            elseif ($type == "kilometre") {
                $amount = $res["queryPrice"] > 0 ? $res['queryPrice'] : $this->kilometerQueryPrice;
                $kilometreResponse = [
                    "success" => $res["isSuccess"],
                    "code" => (int) $res["resultCode"],
                    "message" => $res["message"],
                    "data" => [
                        "id" => $res["kilometerQueryId"],
                        "uniqueId" => $res["processNumber"],
                        "query" => "Kilometre Sorgu Plaka",
                        "date" => date("Y-m-d"),  // Mevcut tarihi alabiliriz ya da dinamik olarak başka bir yerden çekebiliriz
                        "data" => $plaka,
                        "userid" => $authUser->id,  // Bu değeri belirlediğiniz kullanıcı ID'sine göre ayarlayın
                        "amount" => $amount,
                        "imageurl" => $res["imageURL"],
                        "reportURL" => $res["reportURL"],
                        "kilometerQueryDetails" => $res["kilometerQueryDetails"],
                        "kilometerQueryId" => $res["kilometerQueryId"],
                        "branchCode" => $res["branchCode"],
                    ],
                    "from" => "arabasorgula.com"
                ];
                if ($data !== null) {
                    if (!is_null($data->kilometre)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type]['db_name'],
                            'result' =>  json_encode($kilometreResponse, JSON_UNESCAPED_UNICODE),
                            'amount' => $amount,
                            'commission' => $amount > 0 ? $settings->kilometre_sorgu_komisyon_arabasorgula : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $data->update([
                            "user_id" => $authUser->id,
                            "kilometre" => json_encode($kilometreResponse, JSON_UNESCAPED_UNICODE),
                            "kilometre_ucret" => $amount,
                            "kilometre_komisyon" => $amount > 0 ? $settings->kilometre_sorgu_komisyon_arabasorgula : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "kilometre" => json_encode($kilometreResponse, JSON_UNESCAPED_UNICODE),
                        "kilometre_ucret" => $amount,
                        "kilometre_komisyon" => $amount > 0 ? $settings->kilometre_sorgu_komisyon_arabasorgula : 0,
                    ]);
                }
                if ($amount != 0 && $amount != $settings->kilometre_sorgu_arabasorgula) {
                    $settings->kilometre_sorgu_arabasorgula = $amount;
                    $settings->save();
                }
                $res = $kilometreResponse;
            }
            elseif ($type == "AracDetay") {
                $amount = $res['price'] > 0 ? $res['price'] : $this->detailQueryPrice ?? 0;
                $detayResponse = [
                    "success" => $res["isSuccess"],
                    "code" => (int) $res["resultCode"],
                    "message" => $res["message"],
                    "data" => [
                        "id" => $res["sbmQueryResultId"],  // Sabit değer; ihtiyaca göre değiştirin
                        "uniqueId" => $res["processNumber"],
                        "query" => "Araç Detay Sorgu Plaka",
                        "date" => now(),  // Tarihi ihtiyaca göre değiştirin
                        "data" => $plaka,  // Şasi numarası
                        "userid" => \auth()->id(),  // Kullanıcı ID
                        "amount" => $amount,  // Sabit değer; ihtiyaca göre değiştirin
                        "model" => null,
                        "imageurl" => $res["resultImageUrl"],
                        "branchCode" => $res["branchCode"],
                    ],
                    "from" => "arabasorgula.com"
                ];
                // Araç detay sorgusu için
                if ($data !== null) {
                    if (!is_null($data->detail)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type]['db_name'],
                            'result' => json_encode($detayResponse),
                            'amount' => $amount,
                            'commission' => $amount > 0 ? $settings->detail_sorgu_komisyon_arabasorgula : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $data->update([
                            "user_id" => $authUser->id,
                            "detail" => json_encode($detayResponse),
                            "detail_ucret"=>$amount,
                            "detail_komisyon"=>$amount > 0 ? $settings->detail_sorgu_komisyon_arabasorgula : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "detail" => json_encode($detayResponse),
                        "detail_ucret"=>$amount,
                        "detail_komisyon"=>$amount > 0 ? $settings->detail_sorgu_komisyon_arabasorgula : 0,
                    ]);
                }

                if ($amount != 0 && $amount != $settings->detail_sorgu_arabasorgula) {
                    $settings->detail_sorgu_arabasorgula = $amount;
                    $settings->save();
                }
                $res = $detayResponse;
            }
            elseif ($type == "Degisen") {
                $amount = $res['price'] > 0 ? $res['price'] : $this->replacementQueryPrice ?? 0;
                $degisenResponse = [
                    "success" => $res["isSuccess"],
                    "code" => (int) $res["resultCode"],
                    "message" => $res["message"],
                    "data" => [
                        "id" => $res['sbmQueryResultId'],  // Sabit değer; ihtiyaca göre değiştirin
                        "uniqueId" => $res["processNumber"],
                        "query" => "Değişen Parça Plaka",
                        "date" => now(),  // Tarihi ihtiyaca göre değiştirin
                        "data" => $plaka,  // Şasi numarası
                        "damagedate" => $date,  // Tarihi ihtiyaca göre değiştirin
                        "userid" => \auth()->id(),  // Kullanıcı ID
                        "result" => $res["message"],
                        "amount" => $amount,  // Sabit değer; ihtiyaca göre değiştirin
                        "model" => null,
                        "imageurl" => $res["resultImageUrl"],
                        "branchCode" => $res["branchCode"],
                    ],
                    "from" => "arabasorgula.com"
                ];

                if ($data !== null) {
                    $commission = $amount > 0 ? $settings->degisen_sorgu_komisyon_arabasorgula : 0;
                    $replacementResponse = json_encode($degisenResponse);

                    DB::table('query_logs_extra')->updateOrInsert([
                        'user_id' => $authUser->id,
                        'expertise_uuid' => $uuid,
                        'type' => $typeMapping[$type]['db_name'],
                        'result' => $replacementResponse,
                        'amount' => $amount,
                        'commission' => $commission,
                    ]);

                    $data->update([
                        "user_id" => $authUser->id,
                        "degisen" => $replacementResponse,
                        "degisen_ucret" => $amount,
                        "degisen_komisyon" => $commission,
                    ]);
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "degisen" => json_encode([$degisenResponse]),
                        "degisen_ucret"=>$amount,
                        "degisen_komisyon"=>$amount > 0 ? $settings->degisen_sorgu_komisyon_arabasorgula : 0,
                    ]);
                }

                if ($amount != 0 && $amount != $settings->degisen_sorgu_arabasorgula) {
                    $settings->degisen_sorgu_arabasorgula = $amount;
                    $settings->save();
                }
                $res = $degisenResponse;
            }
            return response()->json($res);
        } else {
            return response()->json(['error' => $res], 400);  // Hata durumunda mesaj döner
        }
    }




    public function sasi($uuid = null, $sasi = null, $type = null, $tc = null, $date = null,$degisenValue = null)
    {
        // API'ye hangi sorgu tipi için gideceğimizi belirleyen bir mapping
        $typeMapping = [
            'hasar' => ['url' => $this->url.'/sbm/damage','queryType' => 1,'db_name'=>'hasar'], // Hasar Sorgusu
            'kilometre' => ['url' => $this->url.'/kilometer', 'queryType' => '1','db_name'=>'kilometre'], // Kilometre Sorgusu
//            'borc' => ['url' => $this->>$this->url.'/Debt', 'queryType' => null], // Borç Sorgusu
            'AracDetay' => ['url' => $this->url.'/sbm/detail', 'queryType' => 1,'db_name'=>'detail'], // Araç Detay Sorgusu
            'Degisen' => ['url' => $this->url.'/sbm/replacement', 'queryType' => 1,'db_name'=>'degisen'], // Değişen Parça Sorgusu
        ];

        // Sorgu tipi yanlış veya desteklenmiyorsa hatayı döndür
        if (!isset($typeMapping[$type])) {
            return response()->json(['error' => 'Geçersiz sorgu tipi'], 400);
        }

        $authUser = \auth()->user();
        $data = QueryLog::where("uuid", $uuid)->first();




        // Eğer veritabanında daha önce yapılmış bir sorgu varsa onu döndür
        if ($data && !is_null($data->{$typeMapping[$type]['db_name']})) {
            $lastExtraQuery = DB::table('query_logs_extra')
                ->where('expertise_uuid', $uuid)
                ->where('type',$typeMapping[$type]['db_name'])
                ->orderBy('id','desc')
                ->first();
            if ($lastExtraQuery){
                $jsonData = json_decode($lastExtraQuery->result, true);
                if ($type == 'Degisen')
                    return response()->json($jsonData);
                else
                    return response()->json($jsonData['data']);
            }else{
                $jsonData = json_decode($data->{$typeMapping[$type]['db_name']}, true);
                if ($type == 'Degisen')
                    return response()->json($jsonData);
                else
                    return response()->json($jsonData['data']);
            }
        }

        // Tarih belirtilmemişse, bugünün tarihini kullanıyoruz
        if ($date == null) {
            $date = date("Y-m-d");
        }

        // Token'ı alıyoruz
        $token = $this->getApiToken();
        // İstek yapacağımız URL ve QueryType, typeMapping'den alınıyor
        $apiEndpoint = $typeMapping[$type]['url'];
        $queryType = $typeMapping[$type]['queryType'];

        // POST verilerini oluşturuyoruz
        $postData = [
            'QueryText' => $sasi,  // Şasi numarası
            'BranchCode' => $authUser->getBranch->kod ?? '',  // Şube kodu zorunlu olan endpointler için
        ];

        // QueryType varsa ekliyoruz
        if ($queryType !== null) {
            $postData['QueryType'] = (int)$queryType;  // API'nin beklediği formatta gönderiyoruz
        }

        if ($type == 'Degisen'){
            $postData['damageDateKeyValue'] = $degisenValue;
            $postData['damageDate'] = $date;
        }

        // Laravel HTTP Client ile API'ye istek yapıyoruz
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
        ])
            ->timeout(120)
            ->post($apiEndpoint, $postData);

        // Yanıtı JSON formatında çözümlüyoruz
        $res = json_decode($response->body(),true);

        $settings = Setting::first();
        // Sorgu başarılıysa veritabanına kaydediyoruz
        if (isset($res['isSuccess']) && $res['isSuccess']) {
            if ($type == "hasar") {
                $amount = $res['price'] > 0 ? $res['price'] : $this->damageQueryPrice ?? 0;

                // 'resultCode' değeri 04 ise özel bir mesaj ayarla
                $message = ($res["resultCode"] === "04")
                    ? "Sorgulama sonucu ilgili aracın SBM kayıtlarında hasar kaydı bulunamamıştır."
                    : $res["message"];
                $hasarResponse = [
                    "success" => $res["isSuccess"],
                    "code" => (int) $res["resultCode"],
                    "message" => $message,  // Mesaj buraya eklendi
                    "data" => [
                        "id" => $res["sbmQueryResultId"],
                        "uniqueId" => $res["processNumber"],
                        "query" => "Hasar Sorgu Şasi",
                        "date" => now(),
                        "data" => $sasi,
                        "userid" => \auth()->id(),  // Kullanıcı ID
                        "result" => $message,  // Mesaj buraya da eklendi
                        "amount" => $amount,
                        "model" => "",
                        "imageurl" => $res["resultImageUrl"],
                        "damageDateList" => [
                            "success" => true,
                            "code" => $res["resultCode"],
                            "message" => $message,
                            "total" => $res["damageCount"],
                            "data" => array_map(function($replacement) use ($res) {
                                return [
                                    "id" => $res["sbmQueryResultId"],
                                    "queryid" => $res["sbmQueryResultId"],
                                    "date" => $replacement["damageDate"],
                                    "changingparts" => $replacement["hasReplacement"],
                                    "damageDateKeyValue" => $replacement["damageDateKeyValue"],
                                ];
                            }, $res["replacementDetails"] ?? [])
                        ],
                        "branchCode" => $res["branchCode"],
                    ],
                    "from" => "arabasorgula.com"
                ];

                if ($data !== null) {
                    if (!is_null($data->hasar)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type]['db_name'],
                            'result' => json_encode($hasarResponse),
                            'amount' => $amount,
                            'commission' => $amount > 0 ? $settings->hasar_sorgu_komisyon_arabasorgula : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $data->update([
                            "user_id" => $authUser->id,
                            "hasar" => json_encode($hasarResponse),
                            "hasar_ucret" => $amount,
                            "hasar_komisyon" => $amount > 0 ? $settings->hasar_sorgu_komisyon_arabasorgula : 0,
                        ]);
                    }



                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "hasar" => json_encode($hasarResponse),
                        "hasar_ucret" => $amount,
                        "hasar_komisyon" => $amount > 0 ? $settings->hasar_sorgu_komisyon_arabasorgula : 0,
                    ]);
                }

                if ($amount != 0 && $amount != $settings->hasar_sorgu_arabasorgula) {
                    $settings->hasar_sorgu_arabasorgula = $amount;
                    $settings->save();
                }

                $res = $hasarResponse;
            }

            elseif ($type == "kilometre") {
                $amount = isset($res["queryPrice"]) && $res["queryPrice"] > 0 ? $res["queryPrice"] : $this->kilometerQueryPrice ?? 0;
                $kilometreResponse = [
                    "success" => $res["isSuccess"],
                    "code" => (int) $res["resultCode"],
                    "message" => $res["message"],
                    "data" => [
                        "id" => $res["kilometerQueryId"],
                        "uniqueId" => $res["processNumber"],
                        "query" => "Kilometre Sorgu Şasi",
                        "date" => date("Y-m-d"),  // Mevcut tarihi alabiliriz ya da dinamik olarak başka bir yerden çekebiliriz
                        "data" => $sasi,
                        "userid" => $authUser->id,  // Bu değeri belirlediğiniz kullanıcı ID'sine göre ayarlayın
                        "amount" => $amount,
                        "imageurl" => $res["imageURL"],
                        "reportURL" => $res["reportURL"],
                        "kilometerQueryDetails" => $res["kilometerQueryDetails"],
                        "kilometerQueryId" => $res["kilometerQueryId"],
                        "branchCode" => $res["branchCode"],
                    ],
                    "from" => "arabasorgula.com"
                ];

                if ($data !== null) {
                    if (!is_null($data->kilometre)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type]['db_name'],
                            'result' => json_encode($kilometreResponse),
                            'amount' => $amount,
                            'commission' => $amount > 0 ? $settings->kilometre_sorgu_komisyon_arabasorgula : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $data->update([
                            "user_id" => $authUser->id,
                            "kilometre" => json_encode($kilometreResponse),
                            "kilometre_ucret" => $amount,
                            "kilometre_komisyon" => $amount > 0 ? $settings->kilometre_sorgu_komisyon_arabasorgula : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "kilometre" => json_encode($kilometreResponse),
                        "kilometre_ucret" => $amount,
                        "kilometre_komisyon" => $amount > 0 ? $settings->kilometre_sorgu_komisyon_arabasorgula : 0,
                    ]);
                }
                if ($amount != 0 && $amount != $settings->kilometre_sorgu_arabasorgula) {
                    $settings->kilometre_sorgu_arabasorgula = $amount;
                    $settings->save();
                }
                $res = $kilometreResponse;
            }
            elseif ($type == "AracDetay") {
                $amount = $res['price'] > 0 ? $res['price'] : $this->detailQueryPrice ?? 0;
                $detayResponse = [
                    "success" => $res["isSuccess"],
                    "code" => (int) $res["resultCode"],
                    "message" => $res["message"],
                    "data" => [
                        "id" => $res["sbmQueryResultId"],  // Sabit değer; ihtiyaca göre değiştirin
                        "uniqueId" => $res["processNumber"],
                        "query" => "Araç Detay Sorgu Şasi",
                        "date" => now(),  // Tarihi ihtiyaca göre değiştirin
                        "data" => $sasi,  // Şasi numarası
                        "userid" => \auth()->id(),  // Kullanıcı ID
                        "amount" => $amount,  // Sabit değer; ihtiyaca göre değiştirin
                        "model" => null,
                        "imageurl" => $res["resultImageUrl"],
                        "branchCode" => $res["branchCode"],
                    ],
                    "from" => "arabasorgula.com"
                ];

                if ($data !== null) {
                    if (!is_null($data->detail)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type]['db_name'],
                            'result' => json_encode($detayResponse),
                            'amount' => $amount,
                            'commission' => $amount > 0 ? $settings->detail_sorgu_komisyon_arabasorgula : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $data->update([
                            "user_id" => $authUser->id,
                            "detail" => json_encode($detayResponse),
                            "detail_ucret"=>$amount,
                            "detail_komisyon"=>$amount > 0 ? $settings->detail_sorgu_komisyon_arabasorgula : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "detail" => json_encode($detayResponse),
                        "detail_ucret"=>$amount,
                        "detail_komisyon"=>$amount > 0 ? $settings->detail_sorgu_komisyon_arabasorgula : 0,
                    ]);
                }

                if ($amount != 0 && $amount != $settings->detail_sorgu_arabasorgula) {
                    $settings->detail_sorgu_arabasorgula = $amount;
                    $settings->save();
                }
                $res = $detayResponse;
            }
            elseif ($type == "Degisen") {
                $amount = $res['price'] > 0 ? $res['price'] : $this->replacementQueryPrice ?? 0;
                $degisenResponse = [
                    "success" => $res["isSuccess"],
                    "code" => (int) $res["resultCode"],
                    "message" => $res["message"],
                    "data" => [
                        "id" => $res['sbmQueryResultId'],  // Sabit değer; ihtiyaca göre değiştirin
                        "uniqueId" => $res["processNumber"],
                        "query" => "Değişen Parça Şasi",
                        "date" => now(),  // Tarihi ihtiyaca göre değiştirin
                        "data" => $sasi,  // Şasi numarası
                        "damagedate" => $date,  // Tarihi ihtiyaca göre değiştirin
                        "userid" => \auth()->id(),  // Kullanıcı ID
                        "result" => $res["message"],
                        "amount" => $amount,  // Sabit değer; ihtiyaca göre değiştirin
                        "model" => null,
                        "imageurl" => $res["resultImageUrl"],
                        "branchCode" => $res["branchCode"],
                    ],
                    "from" => "arabasorgula.com"
                ];

                if ($data !== null) {
                    if (!is_null($data->degisen)){
                        DB::table('query_logs_extra')->insert([
                            'user_id' => $authUser->id,
                            'expertise_uuid' => $uuid,
                            'type' => $typeMapping[$type]['db_name'],
                            'result' => json_encode($degisenResponse),
                            'amount' => $amount,
                            'commission' => $amount > 0 ? $settings->degisen_sorgu_komisyon_arabasorgula : 0,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }else{
                        $array = [];
                        if ($data->degisen !== null) {
                            $items = json_decode($data->degisen);
                            if (is_array($items)) {
                                $array = array_merge($array, $items);
                            } else {
                                array_push($array, $items);
                            }
                        }
                        array_push($array, $degisenResponse);

                        $data->update([
                            "user_id" => $authUser->id,
                            "degisen" => json_encode($array),
                            "degisen_ucret"=>$amount,
                            "degisen_komisyon"=>$amount > 0 ? $settings->degisen_sorgu_komisyon_arabasorgula : 0,
                        ]);
                    }
                } else {
                    $data = QueryLog::create([
                        "user_id" => $authUser->id,
                        "uuid" => $uuid,
                        "degisen" => json_encode([$degisenResponse]),
                        "degisen_ucret"=>$amount,
                        "degisen_komisyon"=>$amount > 0 ? $settings->degisen_sorgu_komisyon_arabasorgula : 0,
                    ]);
                }

                if ($amount != 0 && $amount != $settings->degisen_sorgu_arabasorgula) {
                    $settings->degisen_sorgu_arabasorgula = $amount;
                    $settings->save();
                }
                $res = $degisenResponse;
            }
            return response()->json($res['data']);
        } else {
            return response()->json(['error' => $res], 400);  // Hata durumunda mesaj döner
        }
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
